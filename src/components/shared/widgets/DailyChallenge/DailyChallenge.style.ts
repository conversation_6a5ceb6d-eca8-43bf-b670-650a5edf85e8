import { StyleSheet } from 'react-native'
import { withOpacity } from 'core/utils/colorUtils'
import Dark from 'core/constants/themes/dark'

const BORDER_RADIUS = 10

const styles = StyleSheet.create({
    gradientContainer: {
        marginBottom: 4,
        borderRadius: BORDER_RADIUS,
        width: '100%',
        maxWidth: 1000,
        borderColor:Dark.colors.tertiary,
        borderWidth:2
    },
    darkBackgroundContainer: {
        padding: 1,
        borderRadius: BORDER_RADIUS,
        backgroundColor: withOpacity(Dark.colors.gradientBackground, 0.5),
    },
    gradientContentContainer: {
        borderRadius: BORDER_RADIUS,
    },
    placeholderContainer: {
        padding: 8,
        borderRadius: BORDER_RADIUS,
    },
    container: {
        borderRadius: BORDER_RADIUS,
        padding: 14,
        gap: 12,
        backgroundColor: withOpacity(Dark.colors.gradientBackground, 0.75),
        flexDirection: 'row',
        alignItems: 'center',
    },
    textContainer: {
        flex: 1,
    },
    title: {
        color: 'white',
        fontSize: 16,
        marginBottom: 0,
        fontFamily: 'Montserrat-700',
    },
    text: {
        color: 'white',
        fontSize: 12,
        marginVertical: 2,
    },
    image: {
        height: 60,
        width: 60,
        transform: [{ scale: 1.6 }],
        resizeMode: 'contain',
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 8,
    },
    buttonText: {
        color: Dark.colors.secondary,
        fontSize: 12,
        fontFamily: 'Montserrat-600',
        marginRight: 8,
    },
    endsInTextContainer: {
        flexShrink: 1,
        marginTop: 6,
        flexDirection: 'row',
    },
    endsInText: {
        color: Dark.colors.textDark,
        fontSize: 8,
    },
    endsInTextTime: {
        color: 'white',
        marginTop: 4,
        fontSize: 9,
    }
})

export default styles

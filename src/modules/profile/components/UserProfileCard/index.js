import React, { useCallback, useState } from 'react'
import _capitalize from 'lodash/capitalize'
import { BADGES_DETAILS } from '../../constants/badges'
import ProfileBadgeOverlay from '../ProfileBadgeOverlay'
import _toUpper from "lodash/toUpper";
import _isEmpty from "lodash/isEmpty";
import Analytics from "../../../../core/analytics";
import { ANALYTICS_EVENTS } from "../../../../core/analytics/const";
import { PAGE_NAME_KEY, PAGE_NAMES } from "../../../../core/constants/pageNames";
import { showRightPane } from 'molecules/RightPane/RightPane'
import useMediaQuery from 'core/hooks/useMediaQuery'
import CompactUserProfileCard from './Compact'
import ExpandedUserProfileCard from './Expanded'
import EditProfilePage from '../../pages/EditProfilePage';
import PropTypes from 'prop-types';

const UserProfileCard = (props) => {
    const { user, isCurrentUser, userAdditionalInfo } = props
    const { isMobile: isCompactMode } = useMediaQuery()

    const [isBadgeOverlayVisible, setIsBadgeOverlayVisible] = useState(false)

    const onCloseOverlayPressed = useCallback(() => {
        setIsBadgeOverlayVisible(false);
    })

    const onPressBadge = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.BADGE.CLICKED_ON_BADGE, {
            [PAGE_NAME_KEY]: PAGE_NAMES.PROFILE_PAGE,
        });
        setIsBadgeOverlayVisible(true);
    }, []);

    const onPressEditProfile = useCallback(() => {
        showRightPane({
            content: <EditProfilePage />
        })
    })

    const badge = BADGES_DETAILS[_toUpper(user?.badge)];
    const shouldShowBadge = !_isEmpty(badge);

    const ComponentTobeRendered = isCompactMode ? CompactUserProfileCard : ExpandedUserProfileCard

    return (<>
        <ComponentTobeRendered
            badge={badge}
            onPressBadge={onPressBadge}
            shouldShowBadge={shouldShowBadge}
            user={user}
            isCurrentUser={isCurrentUser}
            userAdditionalInfo={userAdditionalInfo}
            onPressEditProfile={onPressEditProfile} />

        {shouldShowBadge && (<ProfileBadgeOverlay
            badge={badge}
            onClose={onCloseOverlayPressed}
            isVisible={isBadgeOverlayVisible}
        />)}
    </>)

}

UserProfileCard.propTypes = {
    isCurrentUser: PropTypes.bool,
    user: PropTypes.object,
    userAdditionalInfo: PropTypes.object
}


export default React.memo(UserProfileCard)

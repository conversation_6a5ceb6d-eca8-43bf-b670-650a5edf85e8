/* eslint-disable no-param-reassign */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { clearStore } from '../helpers';
import { GameState } from './types';
import GameHandlers from './handlers';

const defaultConfig = {
  game: null,
  loading: false,
  error: null,
  lastUpdatedAt: 0,
  initialGame: null,
  currentGame: null,
};

const useGameZustandStore = create<GameState>()(
  immer((set, get) => {
    const handlers = new GameHandlers(set, get);
    return {
      ...defaultConfig,
      ...handlers,
      clearStore: () => {
        clearStore(set, defaultConfig);
      },
    };
  }),
);

export default useGameZustandStore;

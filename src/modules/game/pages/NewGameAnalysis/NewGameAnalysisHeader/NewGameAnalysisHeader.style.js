import { StyleSheet } from "react-native";
import dark from "@/src/core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
      width: '100%',
      justifyContent: 'space-between',
      alignItems: 'center',
      flexDirection: 'row',
      height: 80,
      gap: 0,
    },
    containerMob: {
      width: '100%',
      overflow: 'hidden',
    },
    timerBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      width: 90,
      justifyContent: 'center',
    },
    timerText: {
      marginBottom: -1,
      fontSize: 14,
      fontFamily: 'Montserrat-600',
      color: dark.colors.timerColor,
    },
    timerText2: {
      marginBottom: -1,
      fontSize: 12,
      fontFamily: 'Montserrat-600',
      color: dark.colors.timerColor,  
    },
    backImg: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      resizeMode: 'stretch',
    },
    timerMainContainer:{
      width: 80,
      height: 30,
      marginTop: 52,
      flexDirection: 'row',
      textAlign: 'center',
      alignItems: 'center',
      justifyContent:'space-between',
    },
    timerContainer:{
      width: 56,
      height: 28,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
      borderWidth: 0.5,
      borderColor: dark.colors.tertiary,
      borderRadius: 10,  
    },
    emptyIcon:{
      width: 10,
      height: 10
    }
    
  });

  export default styles;
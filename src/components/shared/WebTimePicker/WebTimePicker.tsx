import { XStack, View, Text } from 'tamagui'
import PrimaryButton from 'atoms/PrimaryButton'
import { StyleSheet } from 'react-native'
import React, { useState } from 'react'

const WebTimePicker = ({
  currentTime,
  onTimeChange,
  onClose,
}: {
  currentTime: Date
  onTimeChange: (hours: number, minutes: number) => void
  onClose: () => void
}) => {
  const [hours, setHours] = useState(currentTime.getHours())
  const [minutes, setMinutes] = useState(currentTime.getMinutes())

  const hoursArray = Array.from({ length: 24 }, (_, i) => i)
  const minutesArray = Array.from({ length: 60 }, (_, i) => i)

  const handleConfirm = () => {
    onTimeChange(hours, minutes)
  }

  return (
    <View style={styles.webPickerContainer}>
      <View style={styles.selectors}>
        <select
          value={hours}
          onChange={(e) => setHours(parseInt(e.target.value))}
          style={styles.select}
        >
          {hoursArray.map((h) => (
            <option key={h} value={h}>
              {h.toString().padStart(2, '0')}
            </option>
          ))}
        </select>

        <Text>:</Text>

        <select
          value={minutes}
          onChange={(e) => setMinutes(parseInt(e.target.value))}
          style={styles.select}
        >
          {minutesArray.map((m) => (
            <option key={m} value={m}>
              {m.toString().padStart(2, '0')}
            </option>
          ))}
        </select>
      </View>

      <XStack space={10} style={styles.buttonRow}>
        <PrimaryButton onPress={handleConfirm} label="Confirm" />
        <PrimaryButton onPress={onClose} label="Cancel" />
      </XStack>
    </View>
  )
}

const styles = StyleSheet.create({
  webPickerContainer: {
    width: 300,
    padding: 16,
  },
  selectors: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    gap: 8,
  },
  select: {
    padding: 8,
    minWidth: 80,
    fontSize: 16,
  },
  buttonRow: {
    justifyContent: 'center',
    marginTop: 10,
  },
})

export default React.memo(WebTimePicker)

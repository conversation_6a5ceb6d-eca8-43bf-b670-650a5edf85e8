import { ImageBackground, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback } from 'react';
import { Button } from '@rneui/base';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

import PrimaryButton from 'atoms/PrimaryButton';
import { useSession } from 'modules/auth/containers/AuthProvider';

import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';

import AntDesign from '@expo/vector-icons/AntDesign';
import backgroundImage from '@/assets/images/splash/SplashScreenBackground.png';
import dark from '../../../../core/constants/themes/dark';
import { PAGE_NAMES } from '../../../../core/constants/pageNames';
import ExpandedRatingFixture from '../ExpandedRatingFixture';
import styles from './RatingFixture.style';
import RatingFixtureSvg from '../../../../components/svg/RatingFixtureIcon';

const RatingFixture = () => {
  const router = useRouter();
  const { user } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();
  const onPressSkip = useCallback(() => router.replace('/home'), []);

  const onPressTakeChallengeNow = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.ONBOARDING.CLICKED_DISCOVER_RATING_TAKE_CHALLENGE_NOW,
      {
        pageName: PAGE_NAMES.DISCOVER_RATING,
      },
    );
    router.push('/fix-your-rating/play');
  }, []);

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <TouchableOpacity onPress={() => router.back()}>
        <AntDesign name="left" size={20} color={dark.colors.textDark} />
      </TouchableOpacity>
      <View style={styles.skipButtonContainer}>
        <Button
          type="clear"
          title="Discover rating later"
          titleStyle={styles.skipButtonTitle}
          onPress={onPressSkip}
        />
        <AntDesign name="doubleright" size={16} color={dark.colors.textDark} />
      </View>
    </View>
  );

  const renderContent = () => (
    <View style={styles.contentContainer}>
      <View style={{ marginLeft: 30 }}>
        <RatingFixtureSvg />
      </View>
      <View style={styles.titleDescriptionContainer}>
        <Text style={styles.title}>DISCOVER RATING</Text>
        <Text style={styles.description}>
          Show your skills in 60 seconds to get matched at your level. You will
          get 20 question to be solved in 60 seconds. Solve faster, get a higher
          rating!
        </Text>
      </View>
    </View>
  );

  const renderFooter = () => (
    <View style={styles.footerContainer}>
      <PrimaryButton
        onPress={onPressTakeChallengeNow}
        label="Discover your rating now!"
        radius={20}
        buttonStyle={{ height: 50, marginBottom: 30, borderRadius: 30 }}
      />
    </View>
  );

  if (!isCompactMode) {
    return (
      <ExpandedRatingFixture
        onPressTakeChallengeNow={onPressTakeChallengeNow}
        onPressSkip={onPressSkip}
      />
    );
  }

  return (
    <View style={styles.container}>
      <ImageBackground
        source={backgroundImage}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          opacity: 0.6,
        }}
        resizeMode="cover"
      />
      {renderHeader()}
      {renderContent()}
      {renderFooter()}
    </View>
  );
};

export default React.memo(RatingFixture);

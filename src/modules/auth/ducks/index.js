import { createSlice } from '@reduxjs/toolkit'

const initialState = {
    user: EMPTY_OBJECT,
}

export const counterSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        addUser: (state) => {
            state.value += 1
        },
        decrement: (state) => {
            state.value -= 1
        },
        incrementByAmount: (state, action) => {
            state.value += action.payload
        },
    },
})

// Action creators are generated for each case reducer function
export const { increment, decrement, incrementByAmount } = counterSlice.actions

export default counterSlice.reducer

import React, { useCallback, useState } from 'react';
import { Text, View } from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';
import { closePopover } from 'molecules/Popover/Popover';
import PropTypes from 'prop-types';
import PrimaryButton from 'atoms/PrimaryButton';
import SecondaryButton from 'atoms/SecondaryButton';
import _map from 'lodash/map';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import styles from './CustomChallengePopover.style';
import dark from '../../../../core/constants/themes/dark';
import UserImage from '../../../../components/atoms/UserImage/UserImage';
import { useSession } from '../../../auth/containers/AuthProvider';
import userReader from '../../../../core/readers/userReader';
import useGameConfig from '../../../home/<USER>/useGameConfig';
import { GAME_CONFIGS } from '../../../home/<USER>/gameConfig';
import useMediaQuery from '../../../../core/hooks/useMediaQuery';
import useChallengeUser from '../../../friendsAndFollowers/hooks/mutations/useChallengeUser';
import { GAME_TYPES } from '../../../home/<USER>/gameTypes';

const CustomChallengePopover = (props) => {
  const { gameConfig: selectedGameConfig, updateGameConfig } = useGameConfig();
  const { opponentUser, gameType } = props;
  const { user: currUser } = useSession();

  const { isCompactMode } = useMediaQuery();

  const onPressClose = useCallback(() => {
    closePopover?.();
  }, []);

  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const { challengeUser } = useChallengeUser();

  const onPressSendChallenge = useCallback(async () => {
    try {
      if (isChallengingFriend) {
        return;
      }
      const playTime = selectedGameConfig.PLAY_TIME;
      const gameConfig = {
        timeLimit: playTime * 60,
        numPlayers: 2,
        gameType: gameType ?? GAME_TYPES.PLAY_ONLINE,
      };
      setIsChallengingFriend(true);
      await challengeUser({
        userId: opponentUser?._id,
        gameConfig,
      });
      setIsChallengingFriend(false);
      closePopover?.();
      closeRightPane?.();
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [
    challengeUser,
    opponentUser?._id,
    selectedGameConfig,
    isChallengingFriend,
    gameType,
  ]);

  const renderGameConfigOption = useCallback(
    ({ option, gameConfig }) => {
      const { key: configKey } = gameConfig;
      const { key, label } = option;
      const isSelected = selectedGameConfig[configKey] === key;

      return (
        <SecondaryButton
          key={key}
          radius={6}
          label={label}
          onPress={() => updateGameConfig({ key: configKey, value: key })}
          containerStyle={[
            styles.buttonContainer,
            isCompactMode && styles.compactButtonContainer,
          ]}
          buttonStyle={[
            styles.timeContainer,
            isSelected && { borderColor: 'white' },
          ]}
          labelStyle={styles.buttonLabelStyle}
        />
      );
    },
    [updateGameConfig, selectedGameConfig],
  );

  const renderGameConfig = useCallback(
    (gameConfig) => {
      const { key, label, options } = gameConfig;

      return (
        <View key={key} style={styles.gameConfigContainer}>
          <View style={styles.configOptionsContainer}>
            {_map(options, (option) =>
              renderGameConfigOption({ option, gameConfig }),
            )}
          </View>
        </View>
      );
    },
    [renderGameConfigOption],
  );

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.headerText}>Create Challenge</Text>
        <Entypo
          name="cross"
          size={20}
          color={dark.colors.textDark}
          onPress={onPressClose}
        />
      </View>
      <View style={styles.usersInfoRow}>
        <View style={styles.userInfo}>
          <UserImage user={currUser} rounded={false} style={styles.userImage} />
          <Text style={styles.userName} numberOfLines={1}>
            You
          </Text>
          <Text style={styles.rating}>{currUser?.rating}</Text>
        </View>
        <Text style={styles.vsText}>VS</Text>
        <View style={styles.userInfo}>
          <UserImage
            user={opponentUser}
            rounded={false}
            style={styles.userImage}
          />
          <Text style={styles.userName} numberOfLines={1}>
            {userReader.username(opponentUser)}
          </Text>
          <Text style={styles.rating}>{opponentUser?.rating}</Text>
        </View>
      </View>

      <View style={styles.configOptionsContainer}>
        {_map(GAME_CONFIGS, (config) => renderGameConfig(config))}
      </View>

      <PrimaryButton
        onPress={onPressSendChallenge}
        label={isChallengingFriend ? 'Challenging.....' : 'Send Challenge'}
        radius={20}
        buttonStyle={{ height: 40, width: 276 }}
      />
    </View>
  );
};

CustomChallengePopover.propTypes = {
  opponentUser: PropTypes.object,
};

export default React.memo(CustomChallengePopover);

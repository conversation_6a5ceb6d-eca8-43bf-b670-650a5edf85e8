import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  topBarContainer: {
    height: 192,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  backgroundImage: {
    width: '100%',
    backgroundColor: dark.colors.tertiary,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    height: 161,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  clubImageContainer: {
    width: '100%',
    position: 'absolute',
    justifyContent: 'center',
    bottom: 0,
    alignItems: 'center',
  },
  clubImageView: {
    height: 80,
    width: 80,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    overflow: 'hidden',
    // borderColor: "red"
  },
  clubName: {
    fontFamily: 'Montserrat-800',
    color: 'white',
    fontSize: 18,
    lineHeight: 16,
    textAlign: 'center',
  },
  clubDesc: {
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    fontSize: 10,
    lineHeight: 15,
    textAlign: 'center',
    paddingHorizontal: 50,
  },
  joinButtonText: {
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
    fontSize: 14,
    // lineHeight: 20,
    textAlign: 'center',
  },
  joinButtonStyle: {
    borderRadius: 20,
    borderWidth: 2,
    borderColor: dark.colors.secondary,
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
    height: 30,
  },
  detailedInfoContainer: {
    flexDirection: 'row',
    flex: 1,
    gap: 8,
  },
  detailValueText: {
    fontFamily: 'Montserrat-600',
    color: 'white',
    fontSize: 12,
    lineHeight: 15,
    textAlign: 'left',
  },
  detailTextContainer: {
    gap: 2,
  },
  detailLabelText: {
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    fontSize: 10,
    lineHeight: 13,
    textAlign: 'left',
  },
  detailIconContainer: {
    height: 30,
    width: 30,
    borderRadius: 6,
    backgroundColor: dark.colors.primary,
  },

  floatingButtonContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    zIndex: 1000,
  },
  addIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 28,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  menuIconButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  fabContainer: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    alignItems: 'center',
    zIndex: 999,
  },
  fab: {
    width: 30,
    height: 30,
    borderRadius: 20,
    backgroundColor: dark.colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1,
  },
  menuButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  menuItemContainer: {
    position: 'absolute',
    right: 0,
    alignItems: 'center',
    flexDirection: 'row',
  },
  menuButtonLabel: {
    color: dark.colors.textDark,
    marginRight: 10,
    fontFamily: 'Montserrat-600',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 998,
  },
  titleStyle: {
    backgroundColor: dark.colors.background,
    color: 'white',
    fontFamily: 'Montserrat-400',
    fontSize: 40,
    padding: 10,
  },
  tabTextContainerStyle: {
    backgroundColor: dark.colors.background,
    width: 120,
  },
  tabTextContainerActiveStyle: {
    backgroundColor: dark.colors.background,
    borderRadius: 0,
    width: 120,
    borderBottomColor: dark.colors.secondary,
    borderBottomWidth: 2,
  },
  tabText: {
    color: 'white',
    fontFamily: 'Montserrat-500',
    fontSize: 12,
  },
  tabTextActiveStyle: {
    color: dark.colors.secondary,
    fontFamily: 'Montserrat-500',
    fontSize: 12,
  },
});

export default styles;

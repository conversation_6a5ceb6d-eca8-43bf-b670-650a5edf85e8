import Dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    width: 260,
    height: 72,
    borderRadius: 14,
    paddingLeft: 12,
    paddingRight: 16,
    paddingVertical: 10,
    backgroundColor: Dark.colors.elevationPrimary,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    borderWidth: 1,
    borderColor: Dark.colors.tertiary,
  },
  imageContainer: {
    height: 44,
    width: 44,
    borderRadius: 8,
  },
  textContainer: {
    flex: 1,
    gap: 6,
  },
  titleLine: {
    height: 14,
    width: '70%',
    borderRadius: 4,
  },
  subtitleLine: {
    height: 12,
    width: '90%',
    borderRadius: 4,
  },
});

export default styles;

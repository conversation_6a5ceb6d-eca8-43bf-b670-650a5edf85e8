import { XStack, View } from 'tamagui'
import PrimaryButton from 'atoms/PrimaryButton'
import { StyleSheet } from 'react-native'
import React, { useState } from 'react'

const WebDatePicker = ({
  currentDate,
  onDateChange,
  onClose,
}: {
  currentDate: Date
  onDateChange: (date: Date) => void
  onClose: () => void
}) => {
  const [tempDate, setTempDate] = useState(currentDate)

  const years = Array.from(
    { length: 10 },
    (_, i) => new Date().getFullYear() - 5 + i
  )
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ]

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate()
  }

  const days = Array.from(
    { length: getDaysInMonth(tempDate.getFullYear(), tempDate.getMonth()) },
    (_, i) => i + 1
  )

  const handleYearChange = (e: any) => {
    const newDate = new Date(tempDate)
    newDate.setFullYear(parseInt(e.target.value))
    setTempDate(newDate)
  }

  const handleMonthChange = (e: any) => {
    const newDate = new Date(tempDate)
    newDate.setMonth(parseInt(e.target.value))
    setTempDate(newDate)
  }

  const handleDayChange = (e: any) => {
    const newDate = new Date(tempDate)
    newDate.setDate(parseInt(e.target.value))
    setTempDate(newDate)
  }

  const handleConfirm = () => {
    onDateChange(tempDate)
  }

  return (
    <View style={styles.webPickerContainer}>
      <View style={styles.selectors}>
        <select
          value={tempDate.getMonth()}
          onChange={handleMonthChange}
          style={styles.select}
        >
          {months.map((month, index) => (
            <option key={month} value={index}>
              {month}
            </option>
          ))}
        </select>

        <select
          value={tempDate.getDate()}
          onChange={handleDayChange}
          style={styles.select}
        >
          {days.map((day) => (
            <option key={day} value={day}>
              {day}
            </option>
          ))}
        </select>

        <select
          value={tempDate.getFullYear()}
          onChange={handleYearChange}
          style={styles.select}
        >
          {years.map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
      </View>

      <XStack space={10} style={styles.buttonRow}>
        <PrimaryButton onPress={handleConfirm} label="Confirm" />
        <PrimaryButton onPress={onClose} label="Cancel" />
      </XStack>
    </View>
  )
}

const styles = StyleSheet.create({
  webPickerContainer: {
    width: 300,
    padding: 16,
  },
  selectors: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    gap: 8,
  },
  select: {
    padding: 8,
    minWidth: 80,
    fontSize: 16,
  },
  buttonRow: {
    justifyContent: 'center',
    marginTop: 10,
  },
})

export default React.memo(WebDatePicker)

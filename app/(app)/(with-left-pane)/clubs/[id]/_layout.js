import { Stack, useLocalSearchParams } from 'expo-router';
import React from 'react';
import WithClubAccess from '@/src/modules/clubs/components/WithClubAccess';

const ClubLayout = () => {
  const { id: clubId } = useLocalSearchParams();

  return (
    <WithClubAccess clubId={clubId}>
      <Stack screenOptions={{ headerShown: false }} />
    </WithClubAccess>
  );
};

export default React.memo(ClubLayout);

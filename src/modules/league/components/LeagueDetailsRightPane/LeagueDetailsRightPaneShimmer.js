import React from 'react';
import { View } from 'react-native';
import ShimmerView from 'molecules/ShimmerView';
import dark from 'core/constants/themes/dark';

const LeagueDetailsRightPaneShimmer = (props) => {
    return (
        <View style={{ marginTop: 15, width: '90%', marginLeft: 31, gap: 15 }}>
            <View style={{ borderColor: dark.colors.tertiary, borderWidth: 2, borderRadius: 10 }}>
                <View style={{ padding: 16, borderRadius: 10, }}>
                    <View style={{ flexDirection: 'row', gap: 10 }}>
                        <ShimmerView
                            style={{ width: 40, height: 40, borderRadius: 8, }}
                            shimmerColors={dark.colors.placeholderShimmerColors}
                        /> 
                        <View style={{ gap: 4 }}>
                            <ShimmerView
                                style={{ width: '70%', height: 18, borderRadius: 3, }}
                                shimmerColors={dark.colors.placeholderShimmerColors}
                            />
                            <ShimmerView
                                style={{ width: 100, height: 10, borderRadius: 3, }}
                                shimmerColors={dark.colors.placeholderShimmerColors}
                            />
                        </View>
                    </View>
                    <View style={{ flexDirection: 'row', gap: 10, marginTop: 10, }}>
                        <ShimmerView
                            style={{ width: 40, height: 40, borderRadius: 8, }}
                            shimmerColors={dark.colors.placeholderShimmerColors}
                        />
                        <View style={{ gap: 4 }}>
                            <ShimmerView
                                style={{ width: '70%', height: 18, borderRadius: 3, }}
                                shimmerColors={dark.colors.placeholderShimmerColors}
                            />
                            <ShimmerView
                                style={{ width: 100, height: 10, borderRadius: 3, }}
                                shimmerColors={dark.colors.placeholderShimmerColors}
                            />
                        </View>
                    </View>
                    <ShimmerView
                        style={{ width: '90%', height: 30, borderRadius: 8, marginHorizontal: 20, marginTop: 20 }}
                        shimmerColors={dark.colors.placeholderShimmerColors}
                    />
                </View>
            </View>
            <View style={{ borderColor: dark.colors.tertiary, borderWidth: 2, borderRadius: 10 }}>
                <View style={{ padding: 16, borderRadius: 10, gap: 10 }}>
                    <ShimmerView
                        style={{ width: '60%', height: 18, borderRadius: 3, }}
                        shimmerColors={dark.colors.placeholderShimmerColors}
                    />
                    <View style={{ flexDirection: 'row', gap: 10 }}>
                        <ShimmerView
                            style={{ width: 40, height: 40, borderRadius: 8, }}
                            shimmerColors={dark.colors.placeholderShimmerColors}
                        />
                        <View style={{ gap: 4 }}>
                            <ShimmerView
                                style={{ width: '70%', height: 18, borderRadius: 3, }}
                                shimmerColors={dark.colors.placeholderShimmerColors}
                            />
                            <ShimmerView
                                style={{ width: 100, height: 10, borderRadius: 3, }}
                                shimmerColors={dark.colors.placeholderShimmerColors}
                            />
                        </View>
                    </View>
                </View>
            </View>
        </View>
    )
}

LeagueDetailsRightPaneShimmer.propTypes = {

};

export default React.memo(LeagueDetailsRightPaneShimmer);
import React, {useEffect} from 'react'

import {Redirect, useLocalSearchParams} from 'expo-router'
import _isEmpty from 'lodash/isEmpty'
import ContestDetails from 'modules/contest/pages/ContestDetails'
import Analytics from "core/analytics";
import {ANALYTICS_EVENTS} from "core/analytics/const";
import {PAGE_NAMES} from "core/constants/pageNames";

const ContestDetailsPage = (props) => {
    const searchParams = useLocalSearchParams()
    const {id: contestId} = searchParams

    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.CONTEST_DETAILS);
        Analytics.track(ANALYTICS_EVENTS.CONTEST.VISITED_CONTEST_DETAIL_PAGE, {contestId})
    }, []);

    if (_isEmpty(contestId)) {
        return <Redirect href="/contests"/>
    }
    return <ContestDetails contestId={contestId}/>
}

export default React.memo(ContestDetailsPage)

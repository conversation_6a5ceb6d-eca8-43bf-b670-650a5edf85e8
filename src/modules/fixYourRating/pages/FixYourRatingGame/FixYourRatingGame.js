import _isEmpty from 'lodash/isEmpty';
import _size from 'lodash/size';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import React, { useCallback, useEffect } from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Loading from 'atoms/Loading';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useRatingFixtureQuestionsState from '../../hooks/useRatingFixtureQuestionsState';
import Header from './Header';

import Question from '../../../game/pages/PlayGame/Question';
import Footer from '../../../game/pages/PlayGame/Footer';

import styles from './FixYourRatingGame.style';
import FixYourRatingResult from '../FixYourRatingResult';

const FixYourRatingGame = React.memo(({ questions }) => {
  const { isMobile } = useMediaQuery();

  // TODO: @mohan check the case when user has submitted but not fixed.
  const {
    submitAnswer,
    currentScore,
    gameEnded,
    currentQuestion,
    timeSpent,
    submittingUserResponse,
    userSubmission,
  } = useRatingFixtureQuestionsState({ questions });

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer question={currentQuestion} submitAnswer={submitAnswer} />
      </View>
    ),
    [currentQuestion, submitAnswer],
  );

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.STARTED_RATING_FIXTURE_GAME);
  }, []);

  if (gameEnded || submittingUserResponse) {
    if (submittingUserResponse || _isEmpty(userSubmission)) {
      return <Loading label="Submitting your answers" />;
    }
    return <FixYourRatingResult submission={userSubmission} />;
  }

  if (_isEmpty(currentQuestion)) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={isMobile && styles.mobileHeader}>
            <Header
              timeSpent={timeSpent}
              currentScore={currentScore}
              maxScore={_size(questions)}
            />
          </View>
          <View style={[styles.question]}>
            <Question question={currentQuestion} />
          </View>
          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
});

const FixYourRatingGameContainer = React.forwardRef((props, ref) => {
  const { questions } = props;

  if (_isEmpty(questions)) return null;

  return <FixYourRatingGame ref={ref} {...props} />;
});

export default React.memo(FixYourRatingGameContainer);

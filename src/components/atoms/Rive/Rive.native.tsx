import React, { useCallback, useEffect, useRef, useState } from 'react';
import Rive, { Fit, RiveRef } from 'rive-react-native';
import {
  AppState,
  AppStateStatus,
  NativeSyntheticEvent,
  Platform,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useRiveCachedUrl } from 'core/hooks/useRiveCache';
import { isValidRiveUrl } from 'atoms/Rive/utils/riveUrlValidation';
import { riveNativeCrashPrevention } from 'atoms/Rive/utils/nativeCrashPrevention';

interface RiveComponentProps {
  url: string;
  resourceName?: string;
  artboardName?: string;
  stateMachineName?: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  fit: Fit;
  onLoopEnd?: () => void;
  fallbackText?: string;
  maxRetries?: number;
  retryDelay?: number;
  enableCrashPrevention?: boolean;
  riveRef?: React.Ref<RiveRef>;
  onStateChanged?: (
    event: NativeSyntheticEvent<{
      stateMachineName: string;
      stateName: string;
    }>,
  ) => void;
}

interface RiveState {
  hasError: boolean;
  isLoading: boolean;
  retryCount: number;
  errorType: 'network' | 'file' | 'runtime' | 'unknown';
  lastError?: Error;
  isValidated?: boolean;
  validationError?: string;
}

const DEFAULT_FALLBACK_TEXT = 'Animation unavailable';
const DEFAULT_MAX_RETRIES = 2;
const DEFAULT_RETRY_DELAY = 1000;

const isNetworkError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('network') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('404') ||
    errorMessage.includes('fetch') ||
    errorMessage.includes('connection')
  );
};

const isFileCorruptionError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('corrupt') ||
    errorMessage.includes('invalid') ||
    errorMessage.includes('malformed') ||
    errorMessage.includes('parse')
  );
};

const RiveComponent: React.FC<RiveComponentProps> = (props) => {
  const {
    url,
    resourceName,
    artboardName,
    stateMachineName,
    style,
    autoPlay = true,
    fit = Fit.Contain,
    loop = true,
    onLoopEnd,
    fallbackText = DEFAULT_FALLBACK_TEXT,
    maxRetries = DEFAULT_MAX_RETRIES,
    retryDelay = DEFAULT_RETRY_DELAY,
    enableCrashPrevention = true,
    riveRef,
    onStateChanged,
  } = props;

  // Use cached URL
  const {
    url: cachedUrl,
    isLoading: isCacheLoading,
    isCached,
  } = useRiveCachedUrl(url);

  const [state, setState] = useState<RiveState>({
    hasError: false,
    isLoading: true,
    retryCount: 0,
    errorType: 'unknown',
    isValidated: false,
  });
  const shouldUseCachedUrl = Platform.OS !== 'android';
  const urlToUse = shouldUseCachedUrl ? cachedUrl : url;

  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef(AppState.currentState);
  const isComponentMountedRef = useRef(true);
  const crashPreventionErrorHandlerRef = useRef<((error: any) => void) | null>(
    null,
  );

  const memoizedStyle = React.useMemo(() => style, [style]);

  useEffect(() => {
    setState({
      hasError: false,
      isLoading: true,
      retryCount: 0,
      errorType: 'unknown',
      isValidated: false,
    });
  }, [url]);

  // File validation effect
  useEffect(() => {
    const validateFile = async () => {
      if (!urlToUse || !enableCrashPrevention) {
        setState((prev) => ({ ...prev, isValidated: true }));
        return;
      }

      try {
        // Only validate local files (cached files)
        if (urlToUse.startsWith('file://')) {
          const validation =
            await riveNativeCrashPrevention.validateBeforeLoad(urlToUse);

          if (!validation.canLoad) {
            Analytics.track(ANALYTICS_EVENTS.RIVE_MALFORMED_FILE_DETECTED, {
              url,
              cachedUrl: urlToUse,
              error: validation.error,
              timestamp: new Date().toISOString(),
            });

            setState((prev) => ({
              ...prev,
              hasError: true,
              isLoading: false,
              errorType: 'file',
              validationError: validation.error,
              isValidated: true,
            }));
            return;
          }

          Analytics.track(ANALYTICS_EVENTS.RIVE_FILE_VALIDATION_SUCCESS, {
            url,
            cachedUrl: urlToUse,
            timestamp: new Date().toISOString(),
          });
        }

        setState((prev) => ({ ...prev, isValidated: true }));
      } catch (error) {
        console.error('Error during file validation:', error);
        setState((prev) => ({
          ...prev,
          isValidated: true,
          validationError: `Validation failed: ${error?.message}`,
        }));
      }
    };

    if (urlToUse && !isCacheLoading) {
      validateFile();
    }
  }, [urlToUse, isCacheLoading, enableCrashPrevention, url]);

  const retryLoad = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    if (state.retryCount < maxRetries) {
      setState((prev) => ({
        ...prev,
        hasError: false,
        isLoading: true,
        retryCount: prev.retryCount + 1,
      }));

      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_RETRY, {
        url,
        retryCount: state.retryCount + 1,
        errorType: state.errorType,
        maxRetries,
      });
    }
  }, [state.retryCount, state.errorType, maxRetries, url]);

  const handleError = useCallback(
    (error: any) => {
      if (!isComponentMountedRef.current) return;

      let errorType: RiveState['errorType'] = 'unknown';

      if (isNetworkError(error)) {
        errorType = 'network';
      } else if (isFileCorruptionError(error)) {
        errorType = 'file';
      } else {
        errorType = 'runtime';
      }

      setState((prev) => ({
        ...prev,
        hasError: true,
        isLoading: false,
        errorType,
        lastError: error,
      }));

      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
        url,
        errorType,
        errorMessage: error?.message || 'Unknown error',
        retryCount: state.retryCount,
        artboardName,
        stateMachineName,
      });

      if (errorType === 'network' && state.retryCount < maxRetries) {
        retryTimeoutRef.current = setTimeout(
          () => {
            retryLoad();
          },
          retryDelay * (state.retryCount + 1),
        );
      }
    },
    [
      state.retryCount,
      maxRetries,
      retryDelay,
      url,
      artboardName,
      stateMachineName,
      retryLoad,
    ],
  );

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        if (state.hasError && state.errorType === 'runtime') {
          setState((prev) => ({
            ...prev,
            hasError: false,
            isLoading: true,
            retryCount: 0,
            errorType: 'unknown',
          }));
        }
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [state.hasError, state.errorType, url]);

  // Setup crash prevention error handler
  useEffect(() => {
    if (!enableCrashPrevention) return;

    const errorHandler = (error: any) => {
      if (!isComponentMountedRef.current) return;

      console.warn('Native crash prevented for Rive component:', error);
      handleError(error);
    };

    crashPreventionErrorHandlerRef.current = errorHandler;
    riveNativeCrashPrevention.addErrorHandler(errorHandler);

    return () => {
      if (crashPreventionErrorHandlerRef.current) {
        riveNativeCrashPrevention.removeErrorHandler(
          crashPreventionErrorHandlerRef.current,
        );
        crashPreventionErrorHandlerRef.current = null;
      }
    };
  }, [enableCrashPrevention, handleError]);

  useEffect(
    () => () => {
      isComponentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      // Clean up crash prevention error handler
      if (crashPreventionErrorHandlerRef.current) {
        riveNativeCrashPrevention.removeErrorHandler(
          crashPreventionErrorHandlerRef.current,
        );
        crashPreventionErrorHandlerRef.current = null;
      }
    },
    [],
  );

  useEffect(
    () => () => {
      isComponentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      // Clean up Rive ref to prevent memory leaks
      try {
        if (riveRef && typeof riveRef === 'object' && 'current' in riveRef) {
          const ref = riveRef.current;
          if (ref) {
            // Try to stop the animation before cleanup
            if (typeof ref.stop === 'function') {
              ref.stop();
            }
            // Clear the ref
            riveRef.current = null;
          }
        }
      } catch (error) {
        console.warn('Error cleaning up Rive ref:', error);
      }
    },
    [riveRef],
  );

  const handleLoad = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    setState((prev) => ({
      ...prev,
      hasError: false,
      isLoading: false,
    }));

    Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_LOADED, {
      url,
      cachedUrl,
      isCached,
      retryCount: state.retryCount,
      artboardName,
      stateMachineName,
    });
  }, [
    url,
    cachedUrl,
    isCached,
    state.retryCount,
    artboardName,
    stateMachineName,
  ]);

  const handleLoopEnd = useCallback(() => {
    try {
      onLoopEnd?.();
    } catch (error) {
      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_CALLBACK_ERROR, {
        url,
        errorMessage: error?.message || 'Loop end callback error',
      });
    }
  }, [onLoopEnd, url]);

  // Show loading state while cache is loading or validating URL

  const onRiveEventReceived = (event) => {
    // These are properties added to the event at Design Time in the
    const eventProperties = event.properties;
    console.info(eventProperties, event, 'infooooooo');
  };

  const riveProps = {
    stateMachineName,
    artboardName,
    style,
    onLoopEnd: handleLoopEnd,
    onError: handleError,
    onPlay: handleLoad,
    onStateChanged,
    ...(urlToUse ? { url: urlToUse } : {}),
    ...(resourceName ? { resourceName } : {}),
  };

  // Check if we have either a valid URL or a resourceName
  const hasValidSource = (url && isValidRiveUrl(url)) || resourceName;

  if ((isCacheLoading && shouldUseCachedUrl) || !hasValidSource) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {isCacheLoading ? 'Loading...' : fallbackText}
        </Text>
      </View>
    );
  }

  // Show loading while validating file
  if (enableCrashPrevention && !state.isValidated) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          Validating...
        </Text>
      </View>
    );
  }

  if (state.hasError) {
    const errorText = state.validationError
      ? `${fallbackText} (${state.validationError})`
      : fallbackText;

    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {errorText}
        </Text>
      </View>
    );
  }

  if (enableCrashPrevention) {
    try {
      return (
        <Rive
          fit={fit}
          url={urlToUse}
          stateMachineName={stateMachineName}
          artboardName={artboardName}
          style={memoizedStyle}
          autoplay={autoPlay}
          // loop={loop}
          onLoopEnd={handleLoopEnd}
          onError={handleError}
          onPlay={handleLoad}
          onStateChanged={onStateChanged}
          ref={riveRef}
          {...(riveProps as any)}
        />
      );
    } catch (error) {
      handleError(error);
      return (
        <View style={style}>
          <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
            {fallbackText}
          </Text>
        </View>
      );
    }
  }

  return (
    <Rive
      fit={fit}
      url={urlToUse}
      // resourceName={resourceName}
      stateMachineName={stateMachineName}
      artboardName={artboardName}
      style={memoizedStyle}
      autoplay={autoPlay}
      // loop={loop}
      onLoopEnd={handleLoopEnd}
      onError={handleError}
      onPlay={handleLoad}
      onStateChanged={onStateChanged}
      ref={riveRef}
      {...(riveProps as any)}
      enableRiveAssetCDN
    />
  );
};

export default React.memo(RiveComponent);

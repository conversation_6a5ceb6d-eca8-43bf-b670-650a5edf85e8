import React, {useEffect} from 'react'

import { Redirect, useLocalSearchParams } from 'expo-router'
import _isEmpty from 'lodash/isEmpty'
import { View, StyleSheet } from 'react-native'
import ContestWaitingPage from 'modules/contest/pages/ContestWaitingPage'
import {PAGE_NAMES} from "../../../../src/core/constants/pageNames";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    },
})

const PlayContests = (props) => {
    const searchParams = useLocalSearchParams()
    const { id: contestId } = searchParams

    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.CONTEST_LIVE_PAGE)
    }, []);

    if (_isEmpty(contestId)) {
        return <Redirect href="/contests" />
    }
    return (
        <View style={styles.container}>
            <ContestWaitingPage contestId={contestId} />
        </View>
    )
}

export default React.memo(PlayContests)

import { View } from 'react-native';
import DropdownComponent from '@/src/components/atoms/Dropdown';
import { DropdownProps } from '../../types/formTypes';
import <PERSON>Field from '../../FormField';
import FormFieldLabel from '../FormFieldLabel';

interface DropdownComponentWithLabelProps {
  field: DropdownProps;
  onValueChange: (text: string) => void;
}

const DropdownComponentWithLabel = (props: DropdownComponentWithLabelProps) => {
  const { field, onValueChange } = props;

  return (
    <View style={[{ gap: 6 }, field.mainContainerStyle]}>
      <FormFieldLabel field={field} />
      <DropdownComponent
        onChange={onValueChange}
        {...field}
        value={field.defaultValue}
      />
    </View>
  );
};

export default DropdownComponentWithLabel;

import { useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import { useMediaQuery as useRNMediaQuery } from 'react-responsive';

const useMediaQuery = () => {
  const isExpandedWindow = useRNMediaQuery({ query: '(min-width: 1500px)' });
  const isRegularWindow = useRNMediaQuery({ query: '(max-width: 1224px)' });
  const isDesktop = useRNMediaQuery({ minWidth: 1025 });
  const isTablet = useRNMediaQuery({ minWidth: 520, maxWidth: 1024 });

  // For React Native (mobile apps), always treat as mobile regardless of screen size
  // This fixes issues with devices like Samsung S24 Ultra being treated as desktop
  const isMobile = Platform.OS !== 'web' || useRNMediaQuery({ maxWidth: 520 });

  const isMobileBrowser = isMobile && Platform.OS === 'web';
  const isWebBrowser = !isMobile && Platform.OS === 'web';
  const isTabletBrowser = isTablet && Platform.OS === 'web';
  const isDesktopBrowser = isDesktop && Platform.OS === 'web';

  return useMemo(
    () => ({
      isExpandedWindow,
      isRegularWindow,
      isDesktop,
      isTablet,
      isMobile,
      isWebBrowser,
      isMobileBrowser,
      isTabletBrowser,
      isDesktopBrowser,
    }),
    [
      isExpandedWindow,
      isRegularWindow,
      isDesktop,
      isTablet,
      isMobile,
      isWebBrowser,
      isMobileBrowser,
      isTabletBrowser,
      isDesktopBrowser,
    ],
  );
};

export default useMediaQuery;

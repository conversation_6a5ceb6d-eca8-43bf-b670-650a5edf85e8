import React, { useCallback } from 'react';
import { KeyboardAvoidingView, Platform, SafeAreaView } from 'react-native';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _trim from 'lodash/trim';
import { FlashList } from '@shopify/flash-list';
import { CreateMessageInput, Message } from '../../types/messages';
import Header from './Header';
import styles from './ChatDetails.style';
import MessageItem from './MessageItem';
import Footer from './Footer';

import useChatDetailBackPressHandler from '../../hooks/useChatDetailBackPressHandler';
import ListFooter from '../ListFooter';
import useChatContext from '../../hooks/useChatContext';

const ChatDetails = ({
  showHeader = true,
  isGroupChat = false,
  handleBackPress,
}: {
  showHeader: boolean;
  isGroupChat: boolean;
  handleBackPress: Function;
}) => {
  const { userId }: any = useSession();

  const {
    loadMoreGroups: loadMore,
    messagesLoading: loading,
    currentGroup: group,
    currentGroupId,
    sendNewMessage,
    messages,
    setCurrentGroupId,
  } = useChatContext();

  const onBackPress = useCallback(() => {
    setCurrentGroupId(null);
    handleBackPress?.();
    return true;
  }, [setCurrentGroupId, handleBackPress]);

  useChatDetailBackPressHandler(onBackPress);
  const renderMessage = useCallback(
    ({ item }: { item: Message }) => (
      <MessageItem item={item} userId={userId} showSenderInfo={isGroupChat} />
    ),
    [userId, isGroupChat],
  );

  const ListFooterComponent = useCallback(
    () => <ListFooter loading={loading} />,
    [loading],
  );
  const sendMessage = useCallback(
    (message: string) => {
      if (_trim(message).length === 0) {
        return;
      }
      const MessagePayload: CreateMessageInput = {
        content: _trim(message),
        groupId: currentGroupId ?? 'EMPTY_ID',
        sender: userId,
        createdAt: new Date(getCurrentTime()),
      };
      sendNewMessage(MessagePayload);
    },
    [sendNewMessage, currentGroupId, userId],
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[styles.container]}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 24}
      >
        {showHeader && <Header group={group} onBackPress={onBackPress} />}
        <FlashList
          data={messages as any[]}
          renderItem={renderMessage}
          keyExtractor={(item, index) => `${item._id}__${index}`}
          contentContainerStyle={styles.messagesList}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={ListFooterComponent as any}
          inverted
          showsVerticalScrollIndicator={false}
        />
        <Footer sendMessage={sendMessage} />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default React.memo(ChatDetails);

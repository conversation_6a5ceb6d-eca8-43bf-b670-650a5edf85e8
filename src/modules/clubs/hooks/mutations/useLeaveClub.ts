import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';

const LEAVE_CLUB_MUTATION = gql`
  mutation LeaveClub($clubId: ID!) {
    leaveClub(clubId: $clubId)
  }
`;

const useLeaveClub = () => {
  const [leaveClubQuery, { loading, error }] = useMutation(LEAVE_CLUB_MUTATION);

  const [isLeavingClub, setIsLeavingClub] = useState(false);

  const leaveClub = useCallback(
    async ({ clubId }: { clubId: string }) => {
      if (isLeavingClub) {
        return false;
      }

      try {
        setIsLeavingClub(true);
        await leaveClubQuery({
          variables: {
            clubId,
          },
        });
        setIsLeavingClub(false);
        return true;
      } catch (e) {
        setIsLeavingClub(false);
        return false;
      } finally {
        setIsLeavingClub(false);
      }
    },
    [leaveClubQuery, isLeavingClub],
  );

  return {
    leaveClub,
    isLeavingClub,
    error,
  };
};

export default useLeaveClub;

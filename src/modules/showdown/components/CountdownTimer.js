/* eslint-disable react/require-default-props */
import React, { useState, useEffect } from 'react'
import { View, Text, StyleSheet } from 'react-native'
import PropTypes from 'prop-types'
import dark from '../../../core/constants/themes/dark'

const styles = StyleSheet.create({
    daysLeftDetails: {
        color: '#D3BFFF',
        fontSize: 12,
        fontFamily: 'Montserrat-600',
    },
    startsInBox: {
        backgroundColor: '#D7C5FF',
        paddingHorizontal: 8,
        width: 160,
        borderRadius: 8,
        alignItems: 'center',
        height: 30,
        justifyContent: 'center',
    },
    startsInText: {
        color: dark.colors.contestText,
        textAlign: 'left',
        fontFamily: 'Montserrat-600',
        fontSize: 14,
    },
})

function CountdownTimer({
    targetDate,
    showOnlyDays = false,
    prefix = 'Starts in',
}) {
    const targetTime = new Date(targetDate).getTime()

    function calculateTimeLeft() {
        const difference = targetTime - new Date().getTime()
        if (difference <= 0) {
            return { days: 0, hours: 0, minutes: 0, seconds: 0 }
        }

        return {
            days: Math.floor(difference / (1000 * 60 * 60 * 24)),
            hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
            minutes: Math.floor((difference / 1000 / 60) % 60),
            seconds: Math.floor((difference / 1000) % 60),
        }
    }

    const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

    useEffect(() => {
        const timer = setInterval(() => {
            setTimeLeft(calculateTimeLeft())
        }, 1000)

        return () => clearInterval(timer)
    }, [targetTime])

    const formatTime = (time) => (time < 10 ? `0${time}` : time)

    if (showOnlyDays) {
        return (
            <Text style={styles.daysLeftDetails}>
                {timeLeft.days > 0
                    ? ` ${formatTime(timeLeft.days)} days`
                    : timeLeft.hours > 0
                      ? ` ${formatTime(timeLeft.hours)} hrs`
                      : timeLeft.minutes > 0
                        ? ` ${formatTime(timeLeft.minutes)} mins`
                        : timeLeft.seconds > 0
                          ? ` ${formatTime(timeLeft.seconds)} secs`
                          : null}
            </Text>
        )
    }

    const label =
        timeLeft.days > 0
            ? `${prefix} ${timeLeft.days} days`
            : `${prefix} ${formatTime(timeLeft.hours)}:${formatTime(timeLeft.minutes)}:${formatTime(timeLeft.seconds)}`

    return (
        <View style={styles.startsInBox}>
            <Text style={styles.startsInText}>{label}</Text>
        </View>
    )
}

CountdownTimer.propTypes = {
    targetDate: PropTypes.number,
    showOnlyDays: PropTypes.bool,
    prefix: PropTypes.string,
}

export default React.memo(CountdownTimer)

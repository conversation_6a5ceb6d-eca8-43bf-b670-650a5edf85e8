import { riveNativeCrashPrevention } from '../nativeCrashPrevention';

// Mock Analytics
jest.mock('core/analytics', () => ({
  track: jest.fn(),
}));

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

describe('RiveNativeCrashPrevention', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    riveNativeCrashPrevention.cleanup();
  });

  it('should initialize without errors', () => {
    expect(() => {
      riveNativeCrashPrevention.initialize();
    }).not.toThrow();
  });

  it('should add and remove error handlers', () => {
    const mockHandler = jest.fn();
    
    riveNativeCrashPrevention.addErrorHandler(mockHandler);
    riveNativeCrashPrevention.removeErrorHandler(mockHandler);
    
    expect(mockHandler).not.toHaveBeenCalled();
  });

  it('should validate files before loading', async () => {
    const result = await riveNativeCrashPrevention.validateBeforeLoad('test.riv');
    
    // On web platform, it should return canLoad: true
    expect(result).toHaveProperty('canLoad');
    expect(typeof result.canLoad).toBe('boolean');
  });

  it('should handle malformed file validation', async () => {
    const result = await riveNativeCrashPrevention.validateBeforeLoad('nonexistent.riv');
    
    expect(result).toHaveProperty('canLoad');
    if (!result.canLoad) {
      expect(result).toHaveProperty('error');
    }
  });
});

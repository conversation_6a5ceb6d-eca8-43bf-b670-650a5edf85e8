import { useEffect, useRef } from 'react';
import { Platform } from 'react-native';

import { useSession } from 'modules/auth/containers/AuthProvider';
import useHandleShowSharePledgeOverlay from 'modules/resolution/hooks/useHandleShowSharePledgeOverlay';
import useHandlePushNotifications from 'core/hooks/useHandlePushNotifications';
import useHandleShowDownloadAppPopover from 'core/utils/useHandleShowDownloadAppPopover';
import usePlatformStats from 'core/graphql/queries/usePlatformStats';
import useWebsocketInit from '@/src/listeners/useWebsocketInit';
import useWebsocketStore from 'store/useWebSocketStore';
import useUserListeners from '@/src/listeners/useUserListeners';
import useWeeklyLeagueModalCheck from '@/src/overlays/hooks/useWeeklyLeagueModalCheck';
import useLoadWasm from './useLoadWasm';
import useTriggerEventsHandler from './useTriggerEventsHandler';
import useMessageHandler from './useMessageHandlers';

const useListeners = () => {
  useWebsocketInit();
  usePlatformStats();
  useLoadWasm();
  useHandleShowDownloadAppPopover();
  useHandleShowSharePledgeOverlay();
  useHandlePushNotifications();
  useUserListeners();
  useMessageHandler();
  useTriggerEventsHandler();
  useWeeklyLeagueModalCheck();

  const hasOfflineListenerSet = useRef<any>(false);

  const { connect, isConnected, ws } = useWebsocketStore((state) => ({
    connect: state.connect,
    isConnected: state.isConnected,
    ws: state.ws,
  }));
  const wsRef = useRef(ws);
  wsRef.current = ws;

  const { session } = useSession();

  useEffect(() => {
    if (!session) return;
    connect(session);
  }, [connect, session]);

    useEffect(() => {
        if (Platform.OS !== 'web') return;

    const handleOffline = () => {
      printDebug('You are offline');
      if (wsRef.current) {
        wsRef.current?.close();
      }
    };

    if (hasOfflineListenerSet.current) {
      hasOfflineListenerSet.current = false;
      window.removeEventListener('offline', handleOffline);
    }

    hasOfflineListenerSet.current = true;
    window.addEventListener('offline', handleOffline);

    return () => {
      if (Platform.OS === 'web') {
        hasOfflineListenerSet.current = false;
        window.removeEventListener('offline', handleOffline);
      }
    };
  }, [isConnected]);
};

export default useListeners;

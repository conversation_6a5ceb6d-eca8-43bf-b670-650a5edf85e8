import {useEffect} from 'react';
import {Platform} from 'react-native';

const useHandleValentineWebsite = () => {
  useEffect(() => {
    if (Platform.OS !== 'web') {
      return;
    }

    const checkUrlAndRedirect = async () => {
      const url = window.location.href;

      if (url) {
        const {host} = new URL(url);
        const {pathname} = window.location;
        if (host === 'valentine.matiks.com' && pathname !== '/valentine') {
          // If the user is not on the `/valentine` route, redirect them
          window.location.href = 'https://matiks.com';
        }
      }
    };

    checkUrlAndRedirect();
  }, []);
};

export default useHandleValentineWebsite;
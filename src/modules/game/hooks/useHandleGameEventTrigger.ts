import { useEffect, useRef } from 'react';
import {
  TriggerPointType,
  TriggerPointEvent,
} from 'store/useTriggerPointsStore/types';
import useTriggerPointsStore from 'store/useTriggerPointsStore';
import _isEmpty from 'lodash/isEmpty';

const useHandleGameEventTrigger = (
  shouldShowNotificationRequestModal: boolean,
  gameId: string,
) => {
  const prevGameIdRef = useRef<string | null>(null);
  const pushTriggerPoint = useTriggerPointsStore(
    (state) => state.pushTriggerPoint,
  );
  const pushTriggerPointRef = useRef(pushTriggerPoint);
  pushTriggerPointRef.current = pushTriggerPoint;
  useEffect(() => {
    if (
      _isEmpty(gameId) ||
      prevGameIdRef.current === gameId ||
      !shouldShowNotificationRequestModal
    )
      return;
    prevGameIdRef.current = gameId;
    pushTriggerPointRef.current(TriggerPointType.DUELS_WIN, [
      TriggerPointEvent.NOTIFICATION_REQUEST,
    ]);
  }, [shouldShowNotificationRequestModal, gameId]);
};

export default useHandleGameEventTrigger;

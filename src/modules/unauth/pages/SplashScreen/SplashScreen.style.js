import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import fonts from '@/src/theme/fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
    width: '100%',
  },
  imageContainer: {
    width: '100%',
    height: '100px',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
  },
  image: {
    width: 300,
    height: 400,
    // width: '60%',
    resizeMode: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    gap: 5,
  },
  googleButtonComponentContainer: {
    minHeight: 40,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 10,
  },
  googleButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    letterSpacing: 1,
    color: dark.colors.textDark,
  },
  dotContainer: {
    height: 5,
    width: 24,
    borderRadius: 20,
    backgroundColor: '#3B3B3B',
    overflow: 'hidden',
  },
  dotFill: {
    height: '100%',
    backgroundColor: dark.colors.secondary,
    borderRadius: 20,
  },
  dot: {
    width: 8,
  },
  activeDot: {
    width: 24,
  },

  titleText: {
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
    letterSpacing: 1,
    lineHeight: 28,
    textAlign: 'center',
  },
  textContainer: {
    maxWidth: 440,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  descriptionText: {
    fontSize: 14,
    textAlign: 'center',
    width: '85%',
    color: dark.colors.textDark,
    marginTop: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: '100%',
    alignItems: 'center',
  },
  getStartedContainer: {
    position: 'absolute',
    bottom: 15,
    width: '90%',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    backgroundColor: dark.colors.tertiary,
    borderRadius: 8,
    height: 40,
  },
  buttonText: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  getStartedButton: {
    backgroundColor: dark.colors.tertiary,
    borderRadius: 8,
    height: 40,
    width: '100%',
  },
  getStartedButtonLabel: {
    color: dark.colors.secondary,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  pagerView: {
    flex: 1,
    height: 300,
    width: '100%',
  },

  colouredTitleText: {
    fontSize: 32,
    fontFamily: 'Montserrat-900',
    color: dark.colors.secondary,
    textAlign: 'center',
  },
  newUserContainer: {
    flexDirection: 'row',
    gap: 8,
    color: 'white',
    marginTop: 10,
  },
  newUserText: {
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
  discoverRatingText: {
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    color: 'white',
  },

  matiksText: {
    fontSize: 36,
    fontFamily: 'Montserrat-900',
    letterSpacing: 4,
    color: dark.colors.secondary,
    textAlign: 'center',
  },
  newUserGetStartedButton: {
    width: '100%',
    minWidth: 200,
    justifyContent: 'center',
    alignItems: 'center',
    // maxWidth: 400,
    height: 60,
    borderRadius: 12,
  },
  newUserGetStartedButtonStyle: {
    borderWidth: 0.8,
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    height: 60,
  },
  alreadyUserBtn: {
    backgroundColor: 'transparent',
    height: 60,
    opacity: 0.6
  },
  alreadyUserTxt: {
    fontWeight: '700',
    fontFamily: fonts.Montserrat_700,
  },
  newUserGetStartedLabel: {
    fontSize: 12,
    letterSpacing: 2,
    fontFamily: 'Montserrat-700',
    color: 'white',
  },
  newUserGetStartedButtonBackground: {
    flex: 1,
    backgroundColor: dark.colors.victoryColor,
  },

  // or styles
  orContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    gap: 16,
    marginTop: 8,
  },
  orSeparator: {
    height: 1,
    width: 100,
    borderRadius: 20,
    backgroundColor: dark.colors.placeholder,
    overflow: 'hidden',
  },
  orText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    letterSpacing: 1,
    color: dark.colors.placeholder,
  },
});

export default styles;

import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const START_SEARCHING_MUTATION = gql`
  mutation AbortSearchingForPuzzleGame {
    abortSearchingForPuzzleGame
  }
`;

const useAbortSearchingForPuzzleGame = () => {
  const [abortSearchingMutation, { data }] = useMutation(
    START_SEARCHING_MUTATION,
  );

  const abortSearching = useCallback(
    () => abortSearchingMutation(),
    [abortSearchingMutation],
  );

  return {
    ...data?.abortSearchingForPuzzleGame,
    abortSearching,
  };
};

export default useAbortSearchingForPuzzleGame;

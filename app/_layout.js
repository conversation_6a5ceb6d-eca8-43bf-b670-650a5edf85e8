/* eslint-disable global-require */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable import/no-unused-modules */
/* eslint-disable react/function-component-definition */
import 'core/utils/polyfill';
import 'core/utils/webEngagePolyfill';
import '../tamagui-web.css';
import '../wdyr';

import * as Sentry from '@sentry/react-native';
import * as SplashScreen from 'expo-splash-screen';

import {
  DefaultTheme,
  ThemeProvider as NavigationThemeProvider,
} from '@react-navigation/native';
import { Dimensions, StyleSheet, Text, View } from 'react-native';
import { createTheme, ThemeProvider as RNEThemeProvider } from '@rneui/themed';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

import { ApolloClientContextProvider } from 'core/contexts/apolloClientContext';
import { ApolloProvider } from '@apollo/client';
import AppUpdateModal from 'shared/AppUpdateModal';
import Bugsnag from '@bugsnag/expo';
import DarkTheme from 'core/constants/themes/dark';
import ErrorView from 'atoms/ErrorView';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import GoogleOAuthProvider from 'core/oauth/containers/GoogleOAuthProvider';
import Loading from 'atoms/Loading';
import { OpenTypeFontProvider } from '@/src/core/contexts/OpenTypeFontContext';
import PropTypes from 'prop-types';
import RiveCacheProvider from '@/src/components/providers/RiveCacheProvider';
import SessionProvider from 'modules/auth/containers/AuthProvider';
import { Slot } from 'expo-router';
import { TamaguiProvider } from 'tamagui';
import WithGrowthBookProvider from 'core/container/WithGrowthBookProvider';
import _isNil from 'lodash/isNil';
import { hideSplashScreen } from 'core/utils/splashScreenHelper';
import { initializeCrashlytics } from '@/src/core/analytics/Crashlytics';
import useAppInitialize from 'core/hooks/appInitialization/useAppInitialize';
import useAppUpdateChecker from 'core/hooks/useAppUpdateChecker/useAppUpdateChecker';
import useColorScheme from 'core/hooks/useColorScheme';
import useHandleValentineWebsite from 'core/hooks/useHandleValentineWebsite';
import useInAppUpdateChecker from 'core/hooks/useInAppUpdateChecker';
import { useUtmTracking } from 'core/utm-tracking/useUTMTracking';
import { tamaguiConfig } from '../tamagui.config';
import 'core/utils/globalErrorHandlers'; // Initialize global error handlers

Sentry.init({
  dsn: process.env.EXPO_PUBLIC_SENTRY_DSN,
  sendDefaultPii: true,
  enabled: process.env.NODE_ENV === 'production',
  // Configure Session Replay
  replaysSessionSampleRate: 0.1,
  profilesSampleRate: 1.0,
  replaysOnErrorSampleRate: 1,
  integrations: [
    Sentry.mobileReplayIntegration(),
    Sentry.feedbackIntegration(),
  ],
  tracesSampleRate: 1.0,
  enableNativeCrashHandling: true,
  enableNative: true,
  enableSpotlight: __DEV__,
  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  ...(__DEV__ ? { spotlight: true } : {}),
});

Bugsnag.start(process.env.EXPO_PUBLIC_BUGSNAG_API_KEY);

global.Bugsnag = Bugsnag;

export { ErrorBoundary } from 'expo-router';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync().catch((error) => {
  /* reloading the app might trigger some race conditions, ignore them */
  console.warn('Error preventing auto hide of splash screen:', error);
});

let isSplashScreenDismissed = false;

const PracticeContext = React.createContext({});

const BugSnagErrorBoundary =
  Bugsnag.getPlugin('react').createErrorBoundary(React);

export const usePracticeContext = () => useContext(PracticeContext);

export default Sentry.wrap(() => {
  useUtmTracking();
  useHandleValentineWebsite();
  useInAppUpdateChecker();
  const {
    apolloClient,
    clientCreationError,
    initializeApolloClient,
    updateApolloClient,
    isAppReady,
    cachedUser,
  } = useAppInitialize();

  useEffect(() => {
    (async () => {
      if (isAppReady && !isSplashScreenDismissed) {
        try {
          isSplashScreenDismissed = true;
          // Use our helper function to safely hide the splash screen
          await hideSplashScreen();
        } catch (e) {
          // handle errors
          console.error('Error hiding splash screen:', e);
        }
      }
    })();
  }, [isAppReady]);

  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      navigator.serviceWorker.register('/service-worker.js');
    }
    initializeCrashlytics?.();
  }, []);

  if (clientCreationError) {
    return (
      <ErrorView
        errorMessage="An error occurred while connecting to server."
        onRetry={initializeApolloClient}
      />
    );
  }

  if (_isNil(apolloClient) || !isAppReady) {
    return <Loading label="Setting up" />;
  }

  return (
    <BugSnagErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ApolloClientContextProvider value={{ updateApolloClient }}>
            <ApolloProvider client={apolloClient}>
              <RootLayoutNav cachedUser={cachedUser} client={apolloClient} />
            </ApolloProvider>
          </ApolloClientContextProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </BugSnagErrorBoundary>
  );
});

Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;

const styles = StyleSheet.create({
  image: {
    flex: 1,
    resizeMode: 'cover',
    justifyContent: 'center',
  },
  overlayContainer: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    position: 'fixed',
  },
});

const OverlayComponents = () => {
  const ToastComponent = require('molecules/Toast').Component;
  const RightPaneComponent = require('molecules/RightPane/RightPane').Component;
  const PopoverComponent = require('molecules/Popover/Popover').Component;
  const BottomSheetComponent =
    require('molecules/BottomSheet/BottomSheet').Component;

  return (
    <>
      <ToastComponent key="ToastComponent" />
      <RightPaneComponent key="RightPaneComponent" />
      <PopoverComponent key="PopoverComponent" />
      <BottomSheetComponent key="BottomSheetComponent" />
    </>
  );
};

const RootLayoutNav = (props) => {
  const { cachedUser, client } = props;

  const {
    showUpdateModal,
    currentVersion,
    latestVersion,
    canSkipUpdate,
    handleUpdate,
    handleSkip,
  } = useAppUpdateChecker();

  const [practiceConfig, setPracticeConfig] = useState();
  const [practiceSession, savePracticeSession] = useState();

  const contextValue = useMemo(
    () => ({
      practiceConfig,
      setPracticeConfig,
      practiceSession,
      savePracticeSession,
    }),
    [practiceConfig, setPracticeConfig, practiceSession, savePracticeSession],
  );

  const windowHeight = Dimensions.get('window').height;
  const colorScheme = useColorScheme();

  const customTheme = createTheme({
    components: {
      Text: {
        style: {
          fontFamily: 'Montserrat-500',
        },
      },
    },
  });

  return (
    <PracticeContext.Provider value={contextValue}>
      <SessionProvider cachedUser={cachedUser} client={client}>
        <WithGrowthBookProvider>
          <GoogleOAuthProvider>
            <OpenTypeFontProvider>
              <RiveCacheProvider autoCache showProgress={false}>
                <TamaguiProvider
                  config={tamaguiConfig}
                  defaultTheme={colorScheme}
                >
                  <RNEThemeProvider theme={customTheme}>
                    <NavigationThemeProvider
                      value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}
                    >
                      <SafeAreaView
                        style={{
                          flex: 1,
                          height: windowHeight,
                          width: '100%',
                          backgroundColor: DarkTheme.colors.background,
                        }}
                      >
                        <Slot />
                        <View
                          style={styles.overlayContainer}
                          pointerEvents="box-none"
                        >
                          <OverlayComponents />
                          <AppUpdateModal
                            visible={showUpdateModal}
                            onClose={handleSkip}
                            onUpdate={handleUpdate}
                            currentVersion={currentVersion}
                            latestVersion={latestVersion}
                            canSkip={canSkipUpdate}
                          />
                        </View>
                      </SafeAreaView>
                    </NavigationThemeProvider>
                  </RNEThemeProvider>
                </TamaguiProvider>
              </RiveCacheProvider>
            </OpenTypeFontProvider>
          </GoogleOAuthProvider>
        </WithGrowthBookProvider>
      </SessionProvider>
    </PracticeContext.Provider>
  );
};

RootLayoutNav.propTypes = {
  cachedUser: PropTypes.object.isRequired,
  client: PropTypes.object.isRequired,
};

// export default AppLayout;

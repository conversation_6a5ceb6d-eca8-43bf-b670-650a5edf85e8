import { differenceInDays, format, isToday, isYesterday } from 'date-fns';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';

export const formatPastTime = (timestamp) => {
  const date = new Date(timestamp);

  if (isToday(date)) {
    // If the date is today, show "Today" and the time
    return `Today, ${format(date, 'hh:mm a')}`;
  }
  if (isYesterday(date)) {
    // If the date is yesterday, show "Yesterday" and the time
    return `Yesterday, ${format(date, 'hh:mm a')}`;
  }
  if (differenceInDays(new Date(), date) <= 7) {
    // For dates within the last week, show the day and time
    return `${format(date, 'dd MMM')}, ${format(date, 'hh:mm a')}`;
  }
  // For older dates, show the full date and time
  return `${format(date, 'dd MMM')}, ${format(date, 'hh:mm a')}`;
};

export const getFormattedCurrentDate = () => {
  const date = new Date(getCurrentTime());
  return format(date, 'yyyy-MM-dd');
};

export const getFormattedTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${Math.floor(remainingSeconds)}`;
};

const getDaySuffix = (day) => {
  if (day > 3 && day < 21) return 'th'; // For 11th, 12th, 13th, etc.
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
};

export const formatDateStringToReadableString = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);

  const day = format(date, 'd'); // Get the day of the month (1-31)
  const suffix = getDaySuffix(day); // Get the appropriate suffix (st, nd, rd, th)
  const formattedDay = `${day}${suffix}`;

  // Format the rest of the date (Month Year)
  const formattedDate = format(date, `MMM yyyy`); // "Jan 2025"

  return `${formattedDay} ${formattedDate}`;
};

export const getTodayPuzzleDate = () => {
  const today = new Date(getCurrentTimeWithOffset());
  return format(today, 'yyyy-MM-dd');
};

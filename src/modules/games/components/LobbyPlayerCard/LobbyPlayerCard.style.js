import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    width: 100,
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    gap: 4,
  },
  ratingText: {
    fontSize: 10,
    letterSpacing: 2,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textLight,
    opacity: 0.6,
    marginBottom: 8
  },
  userImageContainer: { 
    borderRadius: 20,
    marginTop: 8, 
},
userImage:{
  height: 40, 
  width: 40, 
  borderRadius: 5, 
  overflow: 'hidden',
  borderWidth: 1,
  borderColor: dark.colors.textLight,
  borderRadius: 50
},
  statusIcon: {
    position: 'absolute',
    width: 10,
    height: 10,
    backgroundColor: dark.colors.secondary,
    zIndex: 10,
    borderRadius: 50,
    bottom: 0,
    right: 2,
    borderWidth: 2,
    borderColor: dark.colors.background,
  },
  nameText:{
    marginTop: 8,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    textAlign: 'center',
    opacity: 0.9
  }
});

export default styles;

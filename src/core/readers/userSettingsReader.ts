import _get from 'lodash/get';
import { userSettingsTypes } from 'core/types/userSettings';
import { UserSettingsKeyboardType } from '@/src/components/shared/CustomKeyboard/types';
import _isEmpty from 'lodash/isEmpty';

const userSettingsReader = {
  playSound: (userSetting: userSettingsTypes) =>
    _get(userSetting, ['playSound'], true),
  hapticFeedback: (userSetting: userSettingsTypes) =>
    _get(userSetting, ['hapticFeedback'], true),
  keyboardType: (userSetting: userSettingsTypes) => {
    const keyboardType = _get(userSetting, ['keyboardType']);
    if (_isEmpty(keyboardType)) return UserSettingsKeyboardType.TELEPHONE;
    return keyboardType;
  },
};

export default userSettingsReader;

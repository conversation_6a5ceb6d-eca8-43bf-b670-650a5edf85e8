import { Dimensions, StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
      width: '100%',
      alignItems: 'center',
    },
    headerContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingBottom: 18,
    },
    headerText: {
      color: dark.colors.textDark,
      lineHeight: 12,
      fontSize: 10,
      fontFamily: 'Montserrat-500',
    },
    innerContainer: {
      paddingTop: 8,
      paddingHorizontal: isCompactMode ? 16 : 20,
      flex: 1,
      width: isCompactMode ? '100%' : Dimensions.get('window').width * 0.6,
      height: '100%',
      gap: 24,
      borderRadius: isCompactMode ? 0 : 12,
      borderWidth: isCompactMode ? 0 : 1,
      borderColor: dark.colors.tertiary,
      marginVertical: isCompactMode ? 0 : 20,
      justifyContent: 'space-between',
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 20,
      marginBottom: 70,
    },
    questionsWithAnalytics: {
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
      paddingBottom: 20,
    },
    question: {
      flex: 1,
      width: '100%',
      // justifyContent: "center",
      // alignItems: 'center',
      maxHeight: 460,
      maxWidth: 420,
    },
    statsFieldText: {
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
      fontSize: 12,
    },
    currQueIndexText: {
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
      fontSize: 15,
      marginTop: !isCompactMode ? 15 : 0,
      textAlign: 'center',
    },
    valueText: {
      fontFamily: 'Montserrat-600',
      color: 'white',
      fontSize: 14,
    },
    separatorLine: {
      width: '80%',
      height: 1,
      marginVertical: 15,
      backgroundColor: dark.colors.tertiary,
    },
    analysisColumn: {
      gap: 6,
      alignItems: 'center',
    },
    complementsRow: {
      position: 'absolute',
      flexDirection: 'row',
      width: isCompactMode ? '100%' : Dimensions.get('window').width * 0.59,
      marginVertical: isCompactMode ? 0 : 15,
      justifyContent: 'space-between',
      alignItems: 'center',
      bottom: isCompactMode ? 0 : 20,
      paddingHorizontal: 16,
      paddingVertical: 10,
      borderTopColor: dark.colors.tertiary,
      borderTopWidth: 1,
      backgroundColor: dark.colors.background,
    },
    presetIdentifierText: {
      fontSize: 12,
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-500',
    },
    questionText: {
      fontSize: 20,
      color: 'white',
      fontFamily: 'Montserrat-500',
      textAlign: 'center',
    },

    complementText: {
      fontSize: 13,
      color: 'white',
      fontFamily: 'Montserrat-500',
      textAlign: 'center',
    },

    tableContainer: {
      borderWidth: 1,
      width: '100%',
      borderColor: dark.colors.tertiary,
      borderRadius: 8,
    },
    tableHeader: {
      flexDirection: 'row',
      // backgroundColor: ',
      borderBottomWidth: 1,
      borderBottomColor: dark.colors.tertiary,
    },
    tableRow: {
      flexDirection: 'row',
      borderBottomWidth: 1,
      borderBottomColor: dark.colors.tertiary,
    },
    headerCell: {
      flex: 1,
      padding: 10,
      fontSize: 11,
      fontFamily: 'Montserrat-500',
      textAlign: 'center',
      color: dark.colors.textDark,
    },
    metricCell: {
      flex: 1,
      padding: 10,
      fontSize: 10,
      fontFamily: 'Montserrat-500',
      textAlign: 'center',
      color: dark.colors.textDark,
    },
    valueCell: {
      flex: 1,
      padding: 10,
      fontSize: 12,
      fontFamily: 'Montserrat-500',
      textAlign: 'center',
      color: 'white',
    },
  });

const useGameQuestionsDetailedAnalysisStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useGameQuestionsDetailedAnalysisStyles;

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Text } from '@rneui/themed';
import { ActivityIndicator, View } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import _get from 'lodash/get';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import userReader from '@/src/core/readers/userReader';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _map from 'lodash/map';
import _isArray from 'lodash/isArray';
import _size from 'lodash/size';
import _every from 'lodash/every';
import useGetWeeklyCoinsEarned from 'modules/profile/hooks/query/useGetWeeklyCoinsEarned';
import useXpChangeGraphStyles from './XpChangeGraph.style';

const weekDays = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

const chartConfigBase = {
  backgroundGradientFrom: dark.colors.background,
  backgroundGradientTo: dark.colors.background,
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(255, 255, 255, ${opacity * 0.1})`,
  labelColor: (opacity = 1) => dark.colors.textLight,
  propsForBackgroundLines: {
    strokeDasharray: '',
    stroke: dark.colors.textDark,
    strokeWidth: 1,
    opacity: 0.4,
  },
  propsForLabels: {
    fontSize: 10,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
  },
};

const mobileChartConfig = {
  ...chartConfigBase,
  propsForDots: {
    r: '6',
    color: dark.colors.secondary,
  },
};

const webChartConfig = {
  ...chartConfigBase,
  backgroundGradientFrom: dark.colors.gradientBackground,
  backgroundGradientTo: dark.colors.gradientBackground,
  labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  propsForDots: {
    r: '6',
    color: dark.colors.secondary,
  },
};

const CustomLegend = ({ legendData, styles }) => (
  <View style={styles.customLegendContainer}>
    {_map(legendData, (item, index) => (
      <View key={index} style={styles.legendItem}>
        <View
          style={[styles.legendColorBox, { backgroundColor: item.color }]}
        />
        <Text style={styles.legendText}>{item.name}</Text>
      </View>
    ))}
  </View>
);

const XpChangeGraph = ({ user, isCurrentUser }) => {
  const styles = useXpChangeGraphStyles();
  const { isMobile: isCompactMode } = useMediaQuery();
  const [containerWidth, setContainerWidth] = useState(0);
  const { user: currentUser } = useSession();

  const profileUserId = userReader.id(user);
  const currentUserId = userReader.id(currentUser);

  const {
    dailyCoins: profileUserDailyCoins,
    loading: loadingProfileUser,
    error: errorProfileUser,
    refetch: refetchProfileWeeklyCoins,
  } = useGetWeeklyCoinsEarned(profileUserId);

  const {
    dailyCoins: currentUserDailyCoins,
    loading: loadingCurrentUser,
    error: errorCurrentUser,
    refetch: refetchCurrentUserWeeklyCoins,
  } = useGetWeeklyCoinsEarned(currentUserId);
  

  useEffect(() => {
    refetchProfileWeeklyCoins();
    if(currentUser){
      refetchCurrentUserWeeklyCoins();
    }
  }, [])

  const isLoading =
    loadingProfileUser || (!isCurrentUser && loadingCurrentUser);
  const hasError = errorProfileUser || (!isCurrentUser && errorCurrentUser);

  const { chartData, legendData } = useMemo(() => {
    const defaultData = [0, 0, 0, 0, 0, 0, 0];

    const profileDataPoints =
      _isArray(profileUserDailyCoins) && _size(profileUserDailyCoins) === 7
        ? profileUserDailyCoins
        : defaultData;

    const datasets = [
      {
        data: profileDataPoints,
        color: (opacity = 1) =>
          isCurrentUser ? dark.colors.secondary : dark.colors.textDark,
        strokeWidth: 1,
      },
    ];

    const tempLegendData = [
      {
        name: isCurrentUser ? 'Your XP' : `${userReader.username(user)}'s XP`,
        color: isCurrentUser ? dark.colors.secondary : dark.colors.textDark,
      },
    ];

    if (!isCurrentUser) {
      const currentUserDataPoints =
        _isArray(currentUserDailyCoins) && _size(currentUserDailyCoins) === 7
          ? currentUserDailyCoins
          : defaultData;

      datasets.push({
        data: currentUserDataPoints,
        color: (opacity = 1) => dark.colors.secondary,
        strokeWidth: 1,
      });
      tempLegendData.push({ name: 'Your XP', color: dark.colors.secondary });
    }

    return {
      chartData: { labels: weekDays, datasets },
      legendData: tempLegendData,
    };
  }, [profileUserDailyCoins, currentUserDailyCoins, isCurrentUser, user]);

  const onContainerLayout = useCallback((event) => {
    const width = _get(event, 'nativeEvent.layout.width', 0);
    setContainerWidth(width > 0 ? width : 0);
  }, []);

  const chartWidth = Math.max(containerWidth, 0);
  const currentChartConfig = !isCompactMode
    ? webChartConfig
    : mobileChartConfig;

  const allDataEmpty = useMemo(
    () =>
      _every(chartData.datasets, (dataset) =>
        _every(dataset.data, (value) => value === 0),
      ),
    [chartData.datasets],
  );

  const renderContent = () => {
    if (isLoading) {
      return (
        <ActivityIndicator
          color={dark.colors.secondary}
          style={styles.loadingIndicator}
        />
      );
    }
    if (hasError) {
      return <Text style={styles.messageText}>Could not load XP data.</Text>;
    }

    if (chartWidth <= 0) {
      return <View style={{ height: isCompactMode ? 180 : 220 }} />;
    }

    return (
      <View style={styles.graphAreaContainer}>
        <View style={styles.chartWrapper}>
          {!isCurrentUser && (
            <CustomLegend legendData={legendData} styles={styles} />
          )}
          <LineChart
            data={chartData}
            width={chartWidth}
            height={isCompactMode ? 180 : 280}
            withDots
            chartConfig={currentChartConfig}
            style={styles.chart}
            withHorizontalLines
            withVerticalLines={false}
            yAxisSuffix="XP"
            hideLegend
          />
        </View>
      </View>
    );
  };

  return (
    <View style={styles.mainContainer}>
      <Text style={styles.label}>XP Gained this Week</Text>
      {allDataEmpty ? (
        <View style={{ marginVertical: 30 }}>
          <Text style={styles.messageText}>No XP earned this week yet.</Text>
        </View>
      ) : (
        <View style={[styles.container, !isCurrentUser && styles.webContainer]}>
          <View
            style={[
              styles.chartContainer,
              !isCompactMode && styles.webChartContainer,
              isCurrentUser && { height: 190 },
            ]}
            onLayout={onContainerLayout}
          >
            {renderContent()}
          </View>
        </View>
      )}
    </View>
  );
};

XpChangeGraph.propTypes = {
  user: PropTypes.object.isRequired,
  isCurrentUser: PropTypes.bool.isRequired,
};

export default React.memo(XpChangeGraph);

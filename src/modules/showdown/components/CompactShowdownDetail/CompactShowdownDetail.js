import _isEmpty from 'lodash/isEmpty'
import React, { useState, useMemo, useCallback } from 'react'
import { View, Text, TouchableOpacity, Image, Platform } from 'react-native'
import Header from 'shared/Header'
import { StatusBar } from 'react-native'
import ShowdownLogo from 'assets/images/showdown_logo.png'
import _get from 'lodash/get'
import _isNil from 'lodash/isNil'
import styles from './CompactShowdownDetail.style'
import CompactShowdownDetailsTabBarView from './CompactShowdownDetailsTab'
import CompactShowdownDetailsShimmer from '../../shimmer/CompactShowdownShimmer'
import dark from '../../../../core/constants/themes/dark'
import LinearGradient from '../../../../components/atoms/LinearGradient'
import { getShowdownPropertiesToTrack } from '../../utils/showdownEvents'
import { PAGE_NAME_KEY, PAGE_NAMES } from '../../../../core/constants/pageNames'
import ShowDownCTAButton from '../ShowDownCTAButton'
import showdownReader from '../../readers/showdownReader'
import WithClubAccess from '@/src/modules/clubs/components/WithClubAccess'

const CompactShowdownDetails = React.memo(({ state, onAction }) => {
  const {
    showdown: showdownDetails,
    isLive,
    hasEnded,
    hasUserRegistered,
  } = state
  const isNativeDevice = Platform.OS !== 'web'

  const renderJoinNowOrRegisterButton = useMemo(
    () => (
      <View style={styles.CTAContainer}>
        <ShowDownCTAButton state={state} onAction={onAction} />
      </View>
    ),
    [state, onAction]
  )

  const eventProperties = {
    ...getShowdownPropertiesToTrack({ showdown: showdownDetails }),
    [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
  }

  const startTime = showdownReader.startTime(showdownDetails)
  const endTime = showdownReader.endTime(showdownDetails)
  const name = showdownReader.name(showdownDetails)
  const roundsCount = showdownReader.roundsCount(showdownDetails)

  const showdownInfoHeader = useCallback(
    () => (
      <View style={styles.contestDetails}>
        <View style={styles.gradientBox}>
          <Image source={ShowdownLogo} style={styles.iconContainer} />
        </View>
        <View style={styles.contestTimeAndDesc}>
          <View style={styles.detailsRow}>
            <Text style={styles.contestTitle} numberOfLines={1}>
              {name}
            </Text>
            {isLive ? (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Live</Text>
              </View>
            ) : null}
          </View>
          <Text style={styles.hostedBy}>Hosted By Matiks</Text>
          {startTime ? (
            <Text style={styles.detailsText}>
              {new Date(startTime).toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'short',
              })}{' '}
              |{' '}
              {new Date(startTime).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
              })}{' '}
              -{' '}
              {new Date(endTime).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
              })}{' '}
              | {`${roundsCount} rounds`}
            </Text>
          ) : null}
        </View>
      </View>
    ),
    [isLive, name, startTime, endTime, roundsCount]
  )

  return (
    <View style={styles.container}>
      <StatusBar
        translucent
        barStyle="light-content"
        backgroundColor={'#A78A30C2'}
      />
      {isNativeDevice ? (
        <LinearGradient
          colors={dark.colors.showdownDetailPageGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 0.55 }}
          style={styles.gradient}
        />
      ) : null}
      <View style={styles.innerContainer}>
        <Header title="Showdown" isTransparentBg={true} />
        {showdownInfoHeader()}
      </View>
      <View style={{ flex: 1, marginBottom: 60 }}>
        <CompactShowdownDetailsTabBarView state={state} />
      </View>
      {renderJoinNowOrRegisterButton}
    </View>
  )
})

const CompactShowdownDetailsContainer = React.forwardRef((props, ref) => {
  const { state } = props

  const { showdown: showdownDetail } = state ?? EMPTY_OBJECT

  const { clubId } = showdownDetail ?? EMPTY_OBJECT

  if (_isEmpty(state)) {
    return <CompactShowdownDetailsShimmer />
  }

  if (!_isNil(clubId)) {
    return (
      <WithClubAccess clubId={clubId}>
        <CompactShowdownDetails ref={ref} {...props} />
      </WithClubAccess>
    )
  }

  return <CompactShowdownDetails ref={ref} {...props} />
})

export default React.memo(CompactShowdownDetailsContainer)

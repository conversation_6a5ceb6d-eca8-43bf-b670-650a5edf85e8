import { gql, useMutation } from "@apollo/client";
import { useCallback, useState } from "react";

const TAKE_PLEDGE_MUTATION = gql`
    mutation TakePledge($duration: Int) {
         takePledge(duration: $duration)
    }
`

const useTakePledge = () => {
    const [takePledgeMutation] = useMutation(TAKE_PLEDGE_MUTATION)
    const [isTakingPledgeMutationInQueue, setIsTakingPledgeMutationInQueue] = useState(false)

    const takePledge = useCallback(async ({ duration }) => {
        if (isTakingPledgeMutationInQueue) {
            return false
        }
        
        try {
            setIsTakingPledgeMutationInQueue(true)
            const responseOfTakePledge = await takePledgeMutation({
                variables: {
                    duration
                }
            })

            const { data } = responseOfTakePledge ?? EMPTY_OBJECT
            const { takePledge } = data ?? EMPTY_OBJECT
            setIsTakingPledgeMutationInQueue(false)
            return takePledge ?? false
        } catch (e) {
            setIsTakingPledgeMutationInQueue(false)
            return false
        } finally {
            setIsTakingPledgeMutationInQueue(false)
        }
    }, [takePledgeMutation, isTakingPledgeMutationInQueue])

    return {
        takePledge,
        loading: isTakingPledgeMutationInQueue
    }
}

export default useTakePledge
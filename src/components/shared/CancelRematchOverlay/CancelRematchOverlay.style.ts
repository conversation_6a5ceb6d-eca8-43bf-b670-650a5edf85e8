import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  container: {
    backgroundColor: dark.colors.background,
    paddingVertical: 20,
    paddingHorizontal: 30,
    gap: 16,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    alignItems: 'center',
    width: '80%',
    maxWidth: 400,
  },
  userInfo: {
    gap: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },

  userNameText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },

  gameDurationText: {
    fontSize: 14,
    fontFamily: 'Montserrat-700',
    color: 'white',
    textAlign: 'center',
  },

  waitingText: {
    color: dark.colors.textDark,
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    textAlign: 'center',
  },

  timerText: {
    color: dark.colors.text,
    fontSize: 14,
    minWidth: 30,
    letterSpacing: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Montserrat-700',
    lineHeight: 18,
  },
  cancelButton: {
    marginTop: 10,
  },
  cancelButtonText: {
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
    fontSize: 12,
  },
  timerContainer: {
    height: 36,
    backgroundColor: dark.colors.background,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
    flexDirection: 'row',
  },
});

export default styles;

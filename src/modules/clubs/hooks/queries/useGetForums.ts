import { gql, useLazyQuery, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import { CreatorInfo } from './useGetForumThreads';

const DEFAULT_PAGE_SIZE = 50;

const GET_FORUMS = gql`
  query GetForums($clubId: ID!, $page: Int, $pageSize: Int) {
    forums(clubId: $clubId, page: $page, pageSize: $pageSize) {
      results {
        id
        title
        description
        createdAt
        createdBy
        creatorInfo {
          username
          profileImageUrl
        }
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

export interface Forum {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  createdBy: string;
  creatorInfo: CreatorInfo;
}

const useGetForums = ({
  clubId,
  pageSize = DEFAULT_PAGE_SIZE,
}: {
  clubId: string;
  pageSize?: number;
}) => {
  const [fetchGetForumsQuery, { loading, error, refetch }] = useLazyQuery(
    GET_FORUMS,
    {
      fetchPolicy: 'cache-and-network',
      notifyOnNetworkStatusChange: true,
    },
  );

  const fetchForums = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      if (loading) return;

      return fetchGetForumsQuery({
        variables: {
          clubId,
          page: pageNumber,
          pageSize,
        },
      });
    },
    [fetchGetForumsQuery, clubId, pageSize],
  );

  return {
    loading,
    error,
    fetchForums,
  };
};
export { useGetForums };

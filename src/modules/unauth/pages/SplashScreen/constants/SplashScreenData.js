import dark from '@/src/core/constants/themes/dark';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';

export const landingScreenData = [
  {
    resourceName: 'landing',
    title: 'MATIKS',
    subTitle: 'YOUR MIND, YOUR ARENA',
  },
];

export const onboardingScreenData = [
  {
    resourceName: 'duels',
    colouredTitle: 'PLAY, COMPETE \n AND IMPROVE',
    title: 'Live Duels',
    subTitle: 'REAL-TIME BATTLES IN MATH, MEMORY & LOGIC.',
  },
  {
    resourceName: 'compi',
    colouredTitle: 'GET',
    title: 'Win & Rise',
    subTitle: 'EARN RATING TO CLIMB THE LEADERBOARD.',
  },
  {
    resourceName: 'compi',
    colouredTitle: 'GET',
    title: 'Leagues & XP',
    subTitle: 'EARN XP, UNLOCK LEAGUES',
  },
  {
    resourceName: 'compi',
    colouredTitle: 'GET',
    title: 'Death To Doomscrolling',
    subTitle: 'CHASE THE REAL DOPAMINE',
  },
  {
    resourceName: 'compi',
    colouredTitle: 'GET',
    buttonType: 'login',
  },
];

export const splashScreenData = [
  {
    animationLink: RIVE_ANIMATIONS.PANDA_ANIMATION,
    title: [
      {
        text: 'MATH & PUZZLES. ',
      },
      {
        text: 'BUT WITH ',
        color: 'white',
      },
      {
        text: 'DUELS. ',
      },
      {
        text: 'AND A ',
        color: 'white',
      },
      {
        text: 'LEADERBOARD',
      },
    ],
  },
  {
    animationLink: RIVE_ANIMATIONS.PANDA_ANIMATION_COMPETE,
    colouredTitle: 'PLAY, COMPETE \n AND IMPROVE',
    title: [
      {
        text: 'MEET ',
      },
      {
        text: 'MATHLETES.\nFIND YOUR ',
        color: 'white',
      },
      {
        text: 'CREW.',
      },
    ],
  },
  {
    animationLink: RIVE_ANIMATIONS.PANDA_ANIMATION_FAST,
    colouredTitle: 'GET',
    title: [
      {
        text: "YOU'RE FAST. BUT ",
        color: 'white',
      },
      {
        text: 'HOW FAST? ',
      },
      {
        text: "LET'S FIND OUT.",
        color: 'white',
      },
    ],
  },
];

import React, { useEffect } from "react"
import { View } from "react-native"
import GroupPlayChatRoom from "../GroupGamePlay/ChatRoom"

const WithChatRoom = (WrappedComponent) => React.forwardRef((props, ref) => {
    return (
        <View style={{ flex: 1, flexDirection: "row", justifyContent: "space-between", height: "100%" }} ref={ref}>
            <WrappedComponent {...props} />
            <GroupPlayChatRoom {...props} />
        </View>
    )
})

export default WithChatRoom
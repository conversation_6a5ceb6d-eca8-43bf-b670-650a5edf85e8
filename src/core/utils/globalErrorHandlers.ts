import { Platform } from 'react-native';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

/**
 * Global error handlers to catch native crashes and unhandled errors
 * This should be initialized early in the app lifecycle
 */
export class GlobalErrorHandlers {
  private static instance: GlobalErrorHandlers;
  private isInitialized = false;
  private originalErrorHandler: any = null;
  private originalPromiseRejectionHandler: any = null;

  static getInstance(): GlobalErrorHandlers {
    if (!GlobalErrorHandlers.instance) {
      GlobalErrorHandlers.instance = new GlobalErrorHandlers();
    }
    return GlobalErrorHandlers.instance;
  }

  /**
   * Initialize global error handlers
   */
  initialize(): void {
    if (this.isInitialized) return;

    try {
      this.setupGlobalErrorHandlers();
      this.setupUnhandledPromiseRejectionHandler();
      this.isInitialized = true;
      
      Analytics.track(ANALYTICS_EVENTS.RIVE_CRASH_PREVENTION_INITIALIZED, {
        platform: Platform.OS,
        type: 'global_error_handlers',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to initialize global error handlers:', error);
    }
  }

  /**
   * Setup global error handler for React Native
   */
  private setupGlobalErrorHandlers(): void {
    if (Platform.OS === 'web') {
      this.setupWebErrorHandlers();
    } else {
      this.setupNativeErrorHandlers();
    }
  }

  /**
   * Setup error handlers for web platform
   */
  private setupWebErrorHandlers(): void {
    // Handle global errors
    this.originalErrorHandler = window.onerror;
    window.onerror = (message, source, lineno, colno, error) => {
      if (this.isRiveRelatedError(error || new Error(String(message)))) {
        this.handleRiveError(error || new Error(String(message)), 'window.onerror');
        return true; // Prevent default browser error handling
      }
      
      // Call original handler for non-Rive errors
      if (this.originalErrorHandler) {
        return this.originalErrorHandler(message, source, lineno, colno, error);
      }
      return false;
    };

    // Handle unhandled errors
    window.addEventListener('error', (event) => {
      if (this.isRiveRelatedError(event.error)) {
        this.handleRiveError(event.error, 'window.error');
        event.preventDefault();
      }
    });
  }

  /**
   * Setup error handlers for native platforms
   */
  private setupNativeErrorHandlers(): void {
    // Set up global error handler for React Native
    if (global.ErrorUtils) {
      this.originalErrorHandler = global.ErrorUtils.getGlobalHandler();
      global.ErrorUtils.setGlobalHandler((error, isFatal) => {
        if (this.isRiveRelatedError(error)) {
          this.handleRiveError(error, 'ErrorUtils.globalHandler', isFatal);
          return; // Don't call original handler for Rive errors
        }
        
        // Call original handler for non-Rive errors
        if (this.originalErrorHandler) {
          this.originalErrorHandler(error, isFatal);
        }
      });
    }
  }

  /**
   * Setup unhandled promise rejection handler
   */
  private setupUnhandledPromiseRejectionHandler(): void {
    if (Platform.OS === 'web') {
      // Handle unhandled promise rejections on web
      this.originalPromiseRejectionHandler = window.onunhandledrejection;
      window.onunhandledrejection = (event) => {
        if (this.isRiveRelatedError(event.reason)) {
          this.handleRiveError(event.reason, 'unhandledrejection');
          event.preventDefault();
          return;
        }
        
        // Call original handler for non-Rive errors
        if (this.originalPromiseRejectionHandler) {
          this.originalPromiseRejectionHandler(event);
        }
      };
    } else {
      // Handle unhandled promise rejections on native
      const tracking = require('promise/setimmediate/rejection-tracking');
      tracking.enable({
        allRejections: true,
        onUnhandled: (id: any, error: any) => {
          if (this.isRiveRelatedError(error)) {
            this.handleRiveError(error, 'promise_rejection_tracking');
          }
        },
      });
    }
  }

  /**
   * Check if error is related to Rive
   */
  private isRiveRelatedError(error: any): boolean {
    if (!error) return false;

    const errorMessage = error?.message?.toLowerCase() || '';
    const errorStack = error?.stack?.toLowerCase() || '';
    const errorName = error?.name?.toLowerCase() || '';
    
    return (
      errorMessage.includes('rive') ||
      errorMessage.includes('malformed') ||
      errorMessage.includes('librive') ||
      errorStack.includes('rive') ||
      errorStack.includes('librive-android') ||
      errorStack.includes('rivecomponent') ||
      errorName.includes('rive') ||
      // Check for specific Rive error patterns
      errorMessage.includes('animation') && errorMessage.includes('corrupt') ||
      errorMessage.includes('animation') && errorMessage.includes('invalid')
    );
  }

  /**
   * Handle Rive-related errors
   */
  private handleRiveError(error: any, source: string, isFatal?: boolean): void {
    try {
      // Track the error
      Analytics.track(ANALYTICS_EVENTS.RIVE_NATIVE_CRASH_PREVENTED, {
        errorMessage: error?.message || 'Unknown error',
        errorStack: error?.stack,
        errorName: error?.name,
        source,
        isFatal: isFatal || false,
        platform: Platform.OS,
        timestamp: new Date().toISOString(),
        handlerType: 'global_error_handler',
      });

      console.warn('Global Rive error handler prevented crash:', {
        message: error?.message,
        source,
        isFatal,
      });

      // Additional logging for development
      if (__DEV__) {
        console.error('Rive Error Details:', {
          error,
          source,
          isFatal,
          stack: error?.stack,
        });
      }
    } catch (handlingError) {
      console.error('Error while handling Rive error in global handler:', handlingError);
    }
  }

  /**
   * Restore original error handlers
   */
  cleanup(): void {
    if (!this.isInitialized) return;

    try {
      if (Platform.OS === 'web') {
        if (this.originalErrorHandler) {
          window.onerror = this.originalErrorHandler;
        }
        if (this.originalPromiseRejectionHandler) {
          window.onunhandledrejection = this.originalPromiseRejectionHandler;
        }
      } else {
        if (global.ErrorUtils && this.originalErrorHandler) {
          global.ErrorUtils.setGlobalHandler(this.originalErrorHandler);
        }
      }

      this.isInitialized = false;
      this.originalErrorHandler = null;
      this.originalPromiseRejectionHandler = null;
    } catch (error) {
      console.error('Error during global error handlers cleanup:', error);
    }
  }
}

// Export singleton instance
export const globalErrorHandlers = GlobalErrorHandlers.getInstance();

// Auto-initialize on import
globalErrorHandlers.initialize();

export default globalErrorHandlers;

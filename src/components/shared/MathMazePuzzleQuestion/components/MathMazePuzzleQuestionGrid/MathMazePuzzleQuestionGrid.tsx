import React, { useRef, useEffect, useMemo } from 'react';
import { View, Platform, Text } from 'react-native';
import Svg, { Line, Rect } from 'react-native-svg';
import {
  GestureDetector,
  Gesture,
  GestureHandlerRootView,
} from 'react-native-gesture-handler';
import dark from '@/src/core/constants/themes/dark';
import AnimatedTile from '../MathMazePuzzleQuestionAnimatedTile';
import { useMathMazePuzzle } from '../../context';
import { styles } from './MathMazePuzzleQuestionGrid.style';
import { LINE_CONFIG, TILE_CONFIG } from '../../constants/puzzleConstants';
import StartEndTileText from '../MathMazePuzzleQuestionAnimatedTile/components';

const getTileFromGridPosition = (
  localX: number,
  localY: number,
  tileSize: number,
  gridSize: number,
): { row: number; col: number } => {
  const cellDimension = tileSize + TILE_CONFIG.MARGIN * 2;

  const col = Math.floor(localX / cellDimension);
  const row = Math.floor(localY / cellDimension);

  if (row >= 0 && row < gridSize && col >= 0 && col < gridSize) {
    const xInCell = localX - col * cellDimension;
    const yInCell = localY - row * cellDimension;

    if (
      xInCell >= TILE_CONFIG.MARGIN &&
      xInCell < TILE_CONFIG.MARGIN + tileSize &&
      yInCell >= TILE_CONFIG.MARGIN &&
      yInCell < TILE_CONFIG.MARGIN + tileSize
    ) {
      return { row, col };
    }
  }
  return { row: -1, col: -1 };
};

const MathMazePuzzleQuestionGrid: React.FC = () => {
  const { state, onAction } = useMathMazePuzzle();
  const { puzzle, path, tileSize, gridSize, pathConnections, result, target } =
    state;

  const containerRef = useRef<View>(null);
  const containerScreenOffset = useRef({ x: 0, y: 0 });

  const hasReachedEnd = useMemo(
    () =>
      path.some((pathKey) => {
        const [row, col] = pathKey.split('-').map(Number);
        return row === gridSize - 1 && col === gridSize - 1;
      }),
    [path, gridSize],
  );

  const getMazeColor = () => {
    let color: string;
    if (hasReachedEnd && result === target) {
      color = dark.colors.secondary;
    } else if (hasReachedEnd && result !== target) {
      color = dark.colors.wrong;
    } else {
      color = dark.colors.mathsMazeSelectedGridColor;
    }

    return color;
  };

  const gesture = Gesture.Pan()
    .onStart((e) => {
      onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: true } });
      const localX = e.x;
      const localY = e.y;
      const tile = getTileFromGridPosition(localX, localY, tileSize, gridSize);
      if (tile) {
        const key = `${tile.row}-${tile.col}`;
        if (path.length === 0 || path[path.length - 1] !== key) {
          onAction({ type: 'ADD_TO_PATH', payload: tile });
        }
      }
    })
    .onUpdate((e) => {
      const localX = e.x;
      const localY = e.y;
      const tile = getTileFromGridPosition(localX, localY, tileSize, gridSize);
      if (tile) {
        const key = `${tile.row}-${tile.col}`;
        if (path.length === 0 || path[path.length - 1] !== key) {
          onAction({ type: 'ADD_TO_PATH', payload: tile });
        }
      }
    })
    .onFinalize(() => {
      onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: false } });
    })
    .runOnJS(true);

  const onContainerLayout = () => {
    if (containerRef.current) {
      containerRef.current.measure(
        (_x, _y, _width, _height, absoluteX, absoluteY) => {
          containerScreenOffset.current = { x: absoluteX, y: absoluteY };
        },
      );
    }
  };

  const handleMouseDown = (row: number, col: number) => {
    if (Platform.OS === 'web') {
      onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: true } });
      onAction({ type: 'ADD_TO_PATH', payload: { row, col } });
    }
  };

  const handleMouseUp = () => {
    if (Platform.OS === 'web') {
      onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: false } });
    }
  };

  const handleMouseEnter = (row: number, col: number) => {
    if (Platform.OS === 'web' && state.isDragging) {
      const currentTileKey = `${row}-${col}`;
      const lastTileKey =
        state.path.length > 0 ? state.path[state.path.length - 1] : null;

      if (currentTileKey === lastTileKey) {
        return;
      }

      if (state.path.length >= 2) {
        const secondLastTileKey = state.path[state.path.length - 2];
        if (currentTileKey === secondLastTileKey) {
          onAction({ type: 'UNDO' });
          return;
        }
      }

      onAction({ type: 'ADD_TO_PATH', payload: { row, col } });
    }
  };

  useEffect(() => {
    if (Platform.OS === 'web') {
      const globalMouseUpListener = () => {
        if (state.isDragging) {
          onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: false } });
        }
      };
      document.addEventListener('mouseup', globalMouseUpListener);
      return () => {
        document.removeEventListener('mouseup', globalMouseUpListener);
      };
    }
    return NULL_FUN;
  }, [state.isDragging, onAction]);

  if (!puzzle)
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading puzzle...</Text>
      </View>
    );

  const isSelected = (row: number, col: number): boolean =>
    path.includes(`${row}-${col}`);

  return (
    <GestureHandlerRootView style={{}}>
      <GestureDetector gesture={gesture}>
        <View
          style={styles.gridContainer}
          ref={containerRef}
          onLayout={onContainerLayout}
        >
          <Svg
            style={styles.svgOverlay}
            width="100%"
            height="100%"
            pointerEvents="none"
          >
            {pathConnections.map((connection, index) => {
              const [fromRow, fromCol] = connection.from;
              const [toRow, toCol] = connection.to;
              const fromX =
                fromCol * (tileSize + TILE_CONFIG.MARGIN * 2) +
                tileSize / 2 +
                TILE_CONFIG.MARGIN;
              const fromY =
                fromRow * (tileSize + TILE_CONFIG.MARGIN * 2) +
                tileSize / 2 +
                TILE_CONFIG.MARGIN;
              const toX =
                toCol * (tileSize + TILE_CONFIG.MARGIN * 2) +
                tileSize / 2 +
                TILE_CONFIG.MARGIN;
              const toY =
                toRow * (tileSize + TILE_CONFIG.MARGIN * 2) +
                tileSize / 2 +
                TILE_CONFIG.MARGIN;

              return (
                <Line
                  key={`line-${index}`}
                  x1={fromX}
                  y1={fromY}
                  x2={toX}
                  y2={toY}
                  stroke={getMazeColor()}
                  strokeWidth={tileSize * 1.05}
                  strokeLinecap="butt"
                  opacity={LINE_CONFIG.OPACITY}
                />
              );
            })}
            {path.map((pathKey, index) => {
              const [row, col] = pathKey.split('-').map(Number);
              const x =
                col * (tileSize + TILE_CONFIG.MARGIN * 2) +
                tileSize / 2 +
                TILE_CONFIG.MARGIN;
              const y =
                row * (tileSize + TILE_CONFIG.MARGIN * 2) +
                tileSize / 2 +
                TILE_CONFIG.MARGIN;

              return (
                <Rect
                  key={`circle-${index}`}
                  x={x - tileSize / 2}
                  y={y - tileSize / 2}
                  rx={4}
                  width={tileSize * LINE_CONFIG.STROKE_WIDTH_MULTIPLIER}
                  height={tileSize * LINE_CONFIG.STROKE_WIDTH_MULTIPLIER}
                  strokeLinecap="round"
                  fill={getMazeColor()}
                  opacity={LINE_CONFIG.OPACITY}
                />
              );
            })}
          </Svg>

          {puzzle.grid.map((rowItems, rowIndex) => (
            <View key={`row-${rowIndex}`} style={styles.gridRow}>
              {rowItems.map((cell, colIndex) => {
                const key = `${rowIndex}-${colIndex}`;
                const isStart = rowIndex === 0 && colIndex === 0;
                const isEnd =
                  rowIndex === gridSize - 1 && colIndex === gridSize - 1;
                const selected = isSelected(rowIndex, colIndex);

                let borderColor: string;
                let borderRadius = tileSize / 2;
                let backgroundColor = dark.colors.puzzle.primary;
                if (selected) {
                  borderColor = getMazeColor();
                  borderRadius = tileSize / 2 - 10;
                  backgroundColor = getMazeColor();
                } else if (isStart || isEnd) {
                  borderColor = dark.colors.mathsMazeSelectedGridColor;
                  borderRadius = tileSize / 2 - 10;
                  backgroundColor = hasReachedEnd
                    ? dark.colors.secondary
                    : dark.colors.mathsMazeSelectedGridColor;
                } else {
                  borderColor =
                    (rowIndex + colIndex) % 2 === 0
                      ? dark.colors.border
                      : dark.colors.tertiary;
                }

                const tileInteractionProps =
                  Platform.OS === 'web'
                    ? {
                        onMouseDown: () => handleMouseDown(rowIndex, colIndex),
                        onMouseEnter: () =>
                          handleMouseEnter(rowIndex, colIndex),
                        onMouseUp: handleMouseUp,
                      }
                    : {};

                return (
                  <View key={key}>
                    <StartEndTileText
                      isStart={isStart}
                      isEnd={isEnd}
                      tileSize={tileSize}
                    />
                    <AnimatedTile
                      row={rowIndex}
                      col={colIndex}
                      cell={cell}
                      tileSize={tileSize}
                      borderColor={borderColor}
                      isSelected={selected}
                      borderRadius={borderRadius}
                      backgroundColor={backgroundColor}
                      onPress={() => {
                        if (!state.isDragging) {
                          onAction({
                            type: 'ADD_TO_PATH',
                            payload: { row: rowIndex, col: colIndex },
                          });
                        }
                      }}
                      tileProps={tileInteractionProps}
                    />
                  </View>
                );
              })}
            </View>
          ))}
        </View>
      </GestureDetector>
    </GestureHandlerRootView>
  );
};

export default MathMazePuzzleQuestionGrid;

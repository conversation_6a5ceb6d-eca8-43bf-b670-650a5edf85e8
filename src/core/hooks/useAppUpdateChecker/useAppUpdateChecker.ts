import {useCallback, useEffect, useState} from 'react';
import {Platform} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {getStorageState, setStorageItemAsync} from '../useStorageState';
import useGetLatestAppVersion from '../../graphql/queries/useGetLatestAppVersion';
import {isUpdateAvailable} from '../../utils/versionUtils';
import {openAppStore} from '../../utils/storeUtils';
import Analytics from '../../analytics';
import {AppVersion} from '../../../components/shared/AppUpdateModal/types';

const UPDATE_CHECK_STORAGE_KEY = 'app_update_last_check';
const UPDATE_DISMISSED_STORAGE_KEY = 'app_update_dismissed_versions';
const CHECK_INTERVAL_HOURS = 24; // Check once per day

interface UseAppUpdateCheckerReturn {
    showUpdateModal: boolean;
    currentVersion: string;
    latestVersion: AppVersion | null;
    canSkipUpdate: boolean;
    handleUpdate: () => Promise<void>;
    handleSkip: () => void;
    checkForUpdates: () => void;
}

let hasSkippedUpdate = false;

const useAppUpdateChecker = (): UseAppUpdateCheckerReturn => {
    const [showUpdateModal, setShowUpdateModal] = useState(false);
    const [latestVersion, setLatestVersion] = useState<AppVersion | null>(null);
    const [canSkipUpdate, setCanSkipUpdate] = useState(true);
    const [shouldCheck, setShouldCheck] = useState(false);

    const currentVersion = DeviceInfo.getVersion();
    const platform = Platform.OS === 'ios' ? 'iOS' : 'Android';

    const {latestVersions, loading, error} = useGetLatestAppVersion({
        platform,
        skip: !shouldCheck,
    });

    // Check if we should perform update check
    const checkShouldPerformCheck = useCallback(async () => {
        try {
            const lastCheckTime = await getStorageState(UPDATE_CHECK_STORAGE_KEY);
            const now = Date.now();

            if (!lastCheckTime) {
                return true;
            }

            const timeSinceLastCheck = now - parseInt(lastCheckTime, 10);
            const hoursElapsed = timeSinceLastCheck / (1000 * 60 * 60);

            return hoursElapsed >= CHECK_INTERVAL_HOURS;
        } catch (error) {
            Analytics.track('app-update: Error checking last check time', {
                error: error.message,
                platform,
                currentAppVersion: currentVersion,
            });
            return true; // Default to checking if there's an error
        }
    }, [platform]);

    // Check if version was previously dismissed
    const checkIfVersionDismissed = useCallback(
        async (version: string) => {
            try {
                const dismissedVersions = await getStorageState(
                    UPDATE_DISMISSED_STORAGE_KEY,
                );
                if (!dismissedVersions) return false;

                const dismissed = JSON.parse(dismissedVersions);
                return dismissed.includes(version);
            } catch (error) {
                Analytics.track('app-update: Error checking dismissed versions', {
                    error: error.message,
                    platform,
                    currentAppVersion: currentVersion,
                });
                return false;
            }
        },
        [platform],
    );

    // Mark version as dismissed
    const markVersionAsDismissed = useCallback(
        async (version: string) => {
            try {
                const dismissedVersions = await getStorageState(
                    UPDATE_DISMISSED_STORAGE_KEY,
                );
                let dismissed = [];

                if (dismissedVersions) {
                    dismissed = JSON.parse(dismissedVersions);
                }

                if (!dismissed.includes(version)) {
                    dismissed.push(version);
                    await setStorageItemAsync(
                        UPDATE_DISMISSED_STORAGE_KEY,
                        JSON.stringify(dismissed),
                    );
                }
            } catch (error) {
                Analytics.track('app-update: Error marking version as dismissed', {
                    error: error.message,
                    version,
                    platform,
                    currentAppVersion: currentVersion,
                });
            }
        },
        [platform],
    );

    // Update last check time
    const updateLastCheckTime = useCallback(async () => {
        try {
            await setStorageItemAsync(
                UPDATE_CHECK_STORAGE_KEY,
                Date.now().toString(),
            );
        } catch (error) {
            Analytics.track('app-update: Error updating last check time', {
                error: error.message,
                platform,
                currentAppVersion: currentVersion,
            });
        }
    }, [platform]);

    // Handle update button press
    const handleUpdate = useCallback(async () => {
        try {
            Analytics.track('app-update: Update button pressed', {
                currentVersion,
                currentAppVersion: currentVersion,
                latestVersion: latestVersion?.version,
                updateType: latestVersion?.updateType,
                isBlocking: latestVersion?.isBlocking,
                platform,
            });

            await openAppStore();
            setShowUpdateModal(false);
        } catch (error) {
            Analytics.track('app-update: Error opening store', {
                error: error.message,
                currentVersion,
                currentAppVersion: currentVersion,
                latestVersion: latestVersion?.version,
                platform,
            });
        }
    }, [currentVersion, latestVersion, platform]);

    // Handle skip button press
    const handleSkip = useCallback(() => {
        if (latestVersion && !latestVersion.isBlocking) {
            Analytics.track('app-update: Update skipped', {
                currentVersion,
                currentAppVersion: currentVersion,
                latestVersion: latestVersion.version,
                updateType: latestVersion.updateType,
                platform,
            });

            markVersionAsDismissed(latestVersion.version);
            setShowUpdateModal(false);
            hasSkippedUpdate = true;
        }
    }, [currentVersion, latestVersion, platform, markVersionAsDismissed]);

    // Manual check for updates
    const checkForUpdates = useCallback(() => {
        setShouldCheck(true);
    }, []);

    // Initialize update check on mount
    useEffect(() => {
        const initializeUpdateCheck = async () => {
            const shouldPerformCheck = await checkShouldPerformCheck();
            if (shouldPerformCheck) {
                setShouldCheck(true);
            }
        };

        initializeUpdateCheck();
    }, [checkShouldPerformCheck]);

    // Process update check results
    useEffect(() => {
        const processUpdateCheck = async () => {
            if (loading || error || !latestVersions || latestVersions.length === 0) {
                return;
            }

            await updateLastCheckTime();

            // Get the latest version for current platform
            const latest = latestVersions[0];
            if (!latest) return;

            const updateAvailable = isUpdateAvailable(currentVersion, latest.version);

            if (updateAvailable) {
                const wasDismissed = await checkIfVersionDismissed(latest.version);

                // Show modal if update is blocking or if it wasn't dismissed
                if (latest.isBlocking || !hasSkippedUpdate) {
                    setLatestVersion(latest);
                    setCanSkipUpdate(!latest.isBlocking);
                    setShowUpdateModal(true);

                    Analytics.track('app-update: Update modal shown', {
                        currentVersion,
                        currentAppVersion: currentVersion,
                        latestVersion: latest.version,
                        updateType: latest.updateType,
                        isBlocking: latest.isBlocking,
                        wasPreviouslyDismissed: wasDismissed,
                        platform,
                    });
                }
            }
        };

        processUpdateCheck();
    }, [
        latestVersions,
        loading,
        error,
        currentVersion,
        platform,
        updateLastCheckTime,
        checkIfVersionDismissed,
    ]);

    return {
        showUpdateModal,
        currentVersion,
        latestVersion,
        canSkipUpdate,
        handleUpdate,
        handleSkip,
        checkForUpdates,
    };
};

export default useAppUpdateChecker;

/* eslint-disable react/no-array-index-key */
import React, { useCallback, useMemo, useState } from 'react';
import { Image, RefreshControl, ScrollView, Text, View } from 'react-native';
import _get from 'lodash/get';
import _reduce from 'lodash/reduce';
import { FlashList } from '@shopify/flash-list';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Loading from 'atoms/Loading';
import useShowdownStore from 'store/useShowdownStore';
import { PagesComponent } from '@/src/components/shared/PaginatedList/PaginatedList';
import dark from 'core/constants/themes/dark';
import showdownReader from '../../readers/showdownReader';
import styles from './ShowdownLeaderboard.style';
import _isEmpty from 'lodash/isEmpty';

const SCORE: any = {
  1: '1',
  0.5: '0.5',
  0: '0',
};

const LeaderBoardRow = React.memo(({ item, rows, rowWidth }: any) => {
  const rounds = _get(item, ['participant', 'rounds'], []);
  const userInfo = _get(item, ['participant', 'userInfo'], EMPTY_OBJECT);
  const total = useMemo(
    () =>
      _reduce(rounds, (_total, round) => _total + _get(round, 'score', 0), 0),
    [rounds],
  );

  return (
    <View style={styles.row}>
      <View style={[styles.cell, styles.rankCell]}>
        <Text style={styles.rankText}>{item.rank}</Text>
      </View>
      <View style={[styles.cell, styles.nameCell]}>
        <Image
          style={{ width: 32, height: 32 }}
          source={{
            uri: _get(userInfo, ['profileImageUrl'], ''),
          }}
        />
        <View style={{ flex: 1 }}>
          <Text style={styles.name}>{_get(userInfo, ['username'], '')}</Text>
          <Text style={styles.ratingText}>
            {_get(userInfo, ['rating'], '')}
          </Text>
        </View>
      </View>
      <View style={[styles.cell, styles.scoreCell, { width: rowWidth + 10 }]}>
        <Text style={styles.scoreText}>{total}</Text>
      </View>
      {rows.map((_: any, index: number) => (
        <View
          key={`leaderboard-row-${index}`}
          style={[styles.cell, styles.scoreCell, { width: rowWidth }]}
        >
          <Text style={styles.scoreText}>
            {SCORE?.[_get(rounds[index], 'score', 0)] ?? '-'}
          </Text>
        </View>
      ))}
    </View>
  );
});

const Leaderboard = () => {
  const [refreshing, setRefreshing] = useState(false);
  const {
    showdownId,
    totalRounds,
    fetchLeaderboard,
    leaderboardPageNumber,
    leaderboard,
    leaderboardLoading,
    totalPages,
  } = useShowdownStore((state) => ({
    showdownId: state.showdownId,
    totalRounds: showdownReader.roundsCount(state.showdown) ?? 0,
    fetchLeaderboard: state.fetchLeaderboard,
    totalPages: state.totalPages,
    leaderboard: state.leaderboard,
    leaderboardLoading: state.leaderboardLoading,
    leaderboardPageNumber: state.leaderboardPageNumber,
  }));

  const rows = useMemo(() => Array(totalRounds).fill(null), [totalRounds]);
  const { isMobile } = useMediaQuery();
  const rowWidth = isMobile ? 46 : 70;
  const renderLeaderBoardRow = useCallback(
    ({ item }: any) => (
      <LeaderBoardRow rowWidth={rowWidth} item={item} rows={rows} />
    ),
    [rowWidth, rows],
  );

  const changePageNumber = useCallback(
    (pageNumber: number) => {
      fetchLeaderboard(showdownId, pageNumber);
    },
    [fetchLeaderboard, showdownId],
  );

  const renderHeader = useCallback(
    () => (
      <View style={[styles.row, styles.headerRow]}>
        <Text style={[styles.headerTitle]}>Mathlete</Text>
        <Text style={[styles.headerRoundTitle, { width: rowWidth + 10 }]}>
          Total
        </Text>
        {rows.map((_, index) => (
          <Text
            key={`leaderboard-row-${index}`}
            style={[styles.headerRoundTitle, { width: rowWidth }]}
          >
            {index + 1}
          </Text>
        ))}
      </View>
    ),
    [rowWidth, rows],
  );

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchLeaderboard(showdownId, leaderboardPageNumber).finally(() => {
      setRefreshing(false);
    });
  }, [fetchLeaderboard, showdownId, leaderboardPageNumber]);

  const keyExtractor = useCallback(
    (item: any, index: number) =>
      _get(item, ['participant', 'userInfo', 'username'], index.toString()),
    [],
  );

  if (leaderboardLoading && _isEmpty(leaderboard)) {
    return <Loading />;
  }

  const participantsLength = leaderboard.length;
  if (!participantsLength) {
    return (
      <View style={styles.NoDataContainer}>
        <Text style={styles.noDataText}>No data available</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        showsHorizontalScrollIndicator={false}
        horizontal
        style={[styles.container, isMobile && { paddingBottom: 54 }]}
      >
        <FlashList
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={dark.colors.secondary}
              colors={[dark.colors.secondary]}
              progressBackgroundColor={dark.colors.primary}
              progressViewOffset={10}
              title="Refreshing..."
              titleColor={dark.colors.secondary}
            />
          }
          showsVerticalScrollIndicator={false}
          data={leaderboard}
          renderItem={renderLeaderBoardRow}
          keyExtractor={keyExtractor}
          ListHeaderComponent={renderHeader}
        />
      </ScrollView>
      {totalPages > 1 ? (
        <PagesComponent
          setPage={changePageNumber as any}
          totalPages={totalPages}
          page={leaderboardPageNumber}
        />
      ) : null}
    </View>
  );
};

export default React.memo(Leaderboard);

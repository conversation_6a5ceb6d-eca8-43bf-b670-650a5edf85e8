import React from 'react'
import { PuzzleType } from 'shared/CrossMathPuzzleQuestion/types/puzzleType'
import { CrossMathPuzzleQuestionContext } from './context'
import Grid from './components/Grid'
import CrossMathPuzzleActions from './components/Actions'
import CrossMathPuzzleOptions from './components/CrossMathPuzzleOptions'
import useCrossMathPuzzleContextState from './hooks/useCrossMathPuzzleContextState'
import CrossMathPuzzleTimer from './components/CrossMathPuzzleTimer'

export interface CrossMathPuzzleQuestionProps {
  puzzle: PuzzleType
  onSubmitPuzzle: ({ timeSpent }: { timeSpent: number }) => void
  onWrongBoxFill?: () => void
  onWrongCombination?: () => void
  children: React.ReactNode
  shouldCacheTime?: boolean
}

const CrossMathPuzzleQuestionRoot: React.FC<CrossMathPuzzleQuestionProps> = ({
  puzzle,
  onSubmitPuzzle,
  onWrongBoxFill,
  onWrongCombination,
  shouldCacheTime = false,
  children,
}) => {
  const value = useCrossMathPuzzleContextState({
    puzzle,
    onSubmitPuzzle,
    shouldCacheTime,
    onWrongBoxFill,
    onWrongCombination,
  })

  return (
    <CrossMathPuzzleQuestionContext.Provider value={value}>
      {children}
    </CrossMathPuzzleQuestionContext.Provider>
  )
}

const CrossMathPuzzleQuestion = Object.assign(CrossMathPuzzleQuestionRoot, {
  Grid,
  Actions: CrossMathPuzzleActions,
  Options: CrossMathPuzzleOptions,
  Timer: CrossMathPuzzleTimer,
})

export default CrossMathPuzzleQuestion

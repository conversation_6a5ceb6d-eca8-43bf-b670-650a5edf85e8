import React, { useState } from "react"
import { View, Text, TouchableOpacity } from "react-native"
import styles from './FlashAnzanQuestionView.style'
import Header from "shared/Header"
import PropTypes from "prop-types"
import useMediaQuery from "core/hooks/useMediaQuery"
import AntDesign from '@expo/vector-icons/AntDesign';
import dark from "../../../../../../core/constants/themes/dark"

const FlashAnzanQuestionView = (props) => {

    const { gameResults, questionNo } = props

    const { isMobile: isCompactMode } = useMediaQuery()

    const [currQueIndex, setCurrQueIndex] = useState(questionNo)

    return (
        <View style={{ flex: 1 }}>
            <Header />
            <View style={styles.innerContainer}>
                <Text style={{ color: "white", fontFamily: "Montserrat-500", textAlign: "center", fontSize: 20, maxWidth: "80%" }}>
                    {gameResults[currQueIndex]?.numbers.join(" , ")}
                </Text>
            </View>
            <View style={{width:"100%",alignItems:"center",marginBottom:30}}>
                <View style={{ paddingVertical: 10, borderBottomColor: 'white', borderBottomWidth: 1,justifyContent:"center", flexDirection: "row", gap: 10, alignItems: "center", width: 140 }}>
                    <Text style={{ color: "white", fontSize: 16, fontFamily: 'Montserrat-600' }}>
                        {gameResults[currQueIndex]?.correctAnswer}
                    </Text>
                    <View style={{ backgroundColor: dark.colors.secondary, height: 15, width: 15, borderRadius: 7.5, alignItems: "center", justifyContent: "center" }}>
                        <AntDesign name="check" size={10} color={dark.colors.tertiary} />
                    </View>
                </View>
            </View>
            <View style={{ flexDirection: "row", justifyContent: "space-between", width: '99%', marginBottom: isCompactMode ? 30 : 0, marginTop: isCompactMode ? 0 : 100, paddingHorizontal: 16 }}>
                <TouchableOpacity onPress={() => currQueIndex != 0 ? setCurrQueIndex(prev => prev - 1) : null}>
                    <Text style={{ fontSize: 13, fontFamily: 'Montserrat-600', color: currQueIndex == 0 ? dark.colors.tertiary : dark.colors.secondary }}>
                        Previous
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => currQueIndex < gameResults.length - 1 ? setCurrQueIndex(prev => prev + 1) : null}>
                    <Text style={{ fontSize: 13, fontFamily: 'Montserrat-600', color: currQueIndex == gameResults.length - 1 ? dark.colors.tertiary : dark.colors.secondary }}>
                        Next
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    )
}

FlashAnzanQuestionView.propTypes = {
    gameResults: PropTypes.array,
    questionNo: PropTypes.number
}

export default React.memo(FlashAnzanQuestionView)
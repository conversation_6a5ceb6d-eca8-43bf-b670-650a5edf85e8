import _includes from 'lodash/includes';
import { ABILITY_QUESTION_CATEGORY } from 'core/constants/questionCategories';
import _join from 'lodash/join';

const adaptNthRootQuestion = (question) => {
  const { expression, answer } = question;
  const [root, , radicand] = expression;

  const adaptedExpression = [radicand];

  return {
    ...question,
    isPerfectPower: !_includes(answer, '.'),
    nthRoot: root,
    roundOffDecimals: 1,
    expression: adaptedExpression,
  };
};

const adaptExponentQuestion = (question) => {
  const { expression } = question;
  const [base, _, exponent] = expression;
  return {
    ...question,
    base,
    exponent,
  };
};

const adaptLCMandHCFQuestion = (question) => {
  const { expression } = question;
  return {
    ...question,
    expression: _join(expression, ' '),
  };
};

const adaptModQuestion = (question) => {
  const { expression } = question;
  const copiedExpression = [...expression];
  copiedExpression[1] = '%';
  return {
    ...question,
    expression: copiedExpression,
  };
};

export const adaptAbilityQuestion = (question: any) => {
  switch (question.category) {
    case ABILITY_QUESTION_CATEGORY.ROOT:
      return adaptNthRootQuestion(question);
    case ABILITY_QUESTION_CATEGORY.EXPONENT:
      return adaptExponentQuestion(question);
    case ABILITY_QUESTION_CATEGORY.MOD:
      return adaptModQuestion(question);
    case ABILITY_QUESTION_CATEGORY.LCM:
    case ABILITY_QUESTION_CATEGORY.HCF:
      return adaptLCMandHCFQuestion(question);
    default:
      return question;
  }
};

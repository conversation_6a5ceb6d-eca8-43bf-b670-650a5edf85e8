import React from 'react';
import { Text, View } from 'react-native';
import { Line } from 'react-native-svg';
import dark from 'core/constants/themes/dark';
import { LineGraphProps, TooltipData } from '../types';
import { defaultStyles } from '../LineGraph.style';

const DEFAULT_AXIS_COLOR = dark.colors.textLight;

interface TooltipComponentProps {
  tooltipProps?: LineGraphProps['tooltip'];
  activeTooltip: TooltipData | null;
  padding: { top: number; right: number; bottom: number; left: number };
  width: number;
  actualGraphContentHeight: number;
  actualGraphContentWidth: number;
  tooltipLayout: { width: number; height: number } | null;
  setTooltipLayout: (layout: { width: number; height: number } | null) => void;
  styles: typeof defaultStyles;
}

export const Tooltip: React.FC<TooltipComponentProps> = ({
  tooltipProps,
  activeTooltip,
  padding,
  width,
  actualGraphContentHeight,
  actualGraphContentWidth,
  tooltipLayout,
  setTooltipLayout,
  styles,
}) => {
  const renderTooltipContent = () => {
    if (!tooltipProps?.enabled || !activeTooltip) return null;

    let contentNode: React.ReactNode;
    if (tooltipProps.renderContent) {
      contentNode = tooltipProps.renderContent(activeTooltip);
    } else {
      contentNode = (
        <Text style={styles.tooltipText}>
          {activeTooltip.label || activeTooltip.x}: {activeTooltip.y}
        </Text>
      );
    }

    const { screenX } = activeTooltip;
    const tooltipContentWidth = tooltipLayout?.width || 0;

    let tooltipContentLeft = screenX - tooltipContentWidth / 2;

    if (tooltipContentLeft < padding.left) {
      tooltipContentLeft = padding.left;
    }
    if (tooltipContentLeft + tooltipContentWidth > width - padding.right) {
      tooltipContentLeft = width - padding.right - tooltipContentWidth;
    }

    if (tooltipContentWidth > actualGraphContentWidth) {
      tooltipContentLeft = padding.left;
    }

    return (
      <View
        style={[
          defaultStyles.tooltipHostContainer,
          { top: tooltipProps?.fixedTopOffset ?? padding.top / 4 },
        ]}
        pointerEvents="none"
      >
        <View
          style={[
            defaultStyles.tooltipContentContainer,
            { left: tooltipContentLeft },
            tooltipProps?.backgroundColor
              ? { backgroundColor: tooltipProps.backgroundColor }
              : {},
            tooltipProps?.padding !== undefined
              ? { padding: tooltipProps.padding }
              : {},
            tooltipProps?.borderRadius !== undefined
              ? { borderRadius: tooltipProps.borderRadius }
              : {},
          ]}
          onLayout={(event) => {
            const { width: lw, height: lh } = event.nativeEvent.layout;
            if (
              !tooltipLayout ||
              tooltipLayout.width !== lw ||
              tooltipLayout.height !== lh
            ) {
              setTooltipLayout({ width: lw, height: lh });
            }
          }}
        >
          {contentNode}
        </View>
      </View>
    );
  };

  const renderConnector = () => {
    if (
      !tooltipProps?.enabled ||
      !activeTooltip ||
      !tooltipProps.showConnectorLine ||
      !tooltipLayout ||
      !tooltipLayout.height
    )
      return null;

    const hostTopOffset = tooltipProps?.fixedTopOffset ?? padding.top / 4;
    const tooltipBaseY = hostTopOffset + tooltipLayout.height;

    const connectorStartX = activeTooltip.screenX;
    const connectorEndX = activeTooltip.screenX;
    const connectorEndY = padding.top + actualGraphContentHeight;

    const stroke = tooltipProps.connectorLineColor || DEFAULT_AXIS_COLOR;
    const strokeWidth = tooltipProps.connectorLineWidth || 1;
    const strokeDasharray =
      tooltipProps.connectorLineStrokeDasharray === undefined
        ? ''
        : tooltipProps.connectorLineStrokeDasharray;

    return (
      <Line
        x1={connectorStartX}
        y1={tooltipBaseY}
        x2={connectorEndX}
        y2={connectorEndY}
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeDasharray={strokeDasharray}
      />
    );
  };

  return <>{renderTooltipContent()}</>;
};

export const TooltipConnector: React.FC<
  Pick<
    TooltipComponentProps,
    | 'tooltipProps'
    | 'activeTooltip'
    | 'padding'
    | 'actualGraphContentHeight'
    | 'tooltipLayout'
  >
> = ({
  tooltipProps,
  activeTooltip,
  padding,
  actualGraphContentHeight,
  tooltipLayout,
}) => {
  if (
    !tooltipProps?.enabled ||
    !activeTooltip ||
    !tooltipProps.showConnectorLine ||
    !tooltipLayout ||
    !tooltipLayout.height
  )
    return null;

  const hostTopOffset = tooltipProps?.fixedTopOffset ?? padding.top / 4;
  const tooltipBaseY = hostTopOffset + tooltipLayout.height;

  const connectorStartX = activeTooltip.screenX;
  const connectorEndX = activeTooltip.screenX;
  const connectorEndY = padding.top + actualGraphContentHeight;

  const stroke = tooltipProps.connectorLineColor || DEFAULT_AXIS_COLOR;
  const strokeWidth = tooltipProps.connectorLineWidth || 1;
  const strokeDasharray =
    tooltipProps.connectorLineStrokeDasharray === undefined
      ? ''
      : tooltipProps.connectorLineStrokeDasharray;

  return (
    <Line
      x1={connectorStartX}
      y1={tooltipBaseY}
      x2={connectorEndX}
      y2={connectorEndY}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeDasharray={strokeDasharray}
    />
  );
};

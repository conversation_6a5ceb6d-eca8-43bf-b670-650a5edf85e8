import { useCallback, useEffect } from 'react';
import {
  getStorageState,
  setStorageItemAsync,
} from 'core/hooks/useStorageState';
import dark from 'core/constants/themes/dark';
import _isEqual from 'lodash/isEqual';
import _toString from 'lodash/toString';
import CommitmentPass from '../components/CommitmentPass';
import { showPopover } from 'molecules/Popover/Popover';
import useTakePledge from './mutations/takePledge';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useCheckIfPledgeTaken from './queries/checkIfPledgeTaken';
import _isNaN from 'lodash/isNaN';
import _isEmpty from 'lodash/isEmpty';

const useHandleShowSharePledgeOverlay = () => {
  const { takePledge } = useTakePledge();

  const {
    checkIfPledgeTaken,
    data: checkIfPledgeTakenResData,
    loading,
  } = useCheckIfPledgeTaken();

  const handleShowSharePledgeOverlay = useCallback(async () => {
    const toShowPledgeOverlay = await getStorageState('showPledgeShareOverlay');
    const commitmentTime = await getStorageState('commitmentTime');

    if (_isNaN(commitmentTime) || commitmentTime <= 0) {
      checkIfPledgeTaken().then((res) => {
        const { getUserResolution: checkIfPledgeTakenRes } =
          res ?? EMPTY_OBJECT;
        const { duration } = checkIfPledgeTakenRes ?? EMPTY_OBJECT;
        if (!_isEmpty(checkIfPledgeTakenRes) && !_isNaN(duration)) {
          setStorageItemAsync('commitmentTime', _toString(duration));
        }
      });
    }

    if (_isEqual(toShowPledgeOverlay, 'true')) {
      const data = await checkIfPledgeTaken();
      const isTookedPledge = !_isEmpty(data?.getUserResolution);
      const pledgeDuration = await getStorageState('pledgeDuration');
      const pledgeDurationInt = parseInt(pledgeDuration);

      if (pledgeDurationInt > 0) {
        await setStorageItemAsync('showPledgeShareOverlay', null);
        await setStorageItemAsync('pledgeDuration', null);
        await setStorageItemAsync(
          'commitmentTime',
          _toString(pledgeDurationInt),
        );

        if (isTookedPledge) {
          showToast({
            type: TOAST_TYPE.INFO,
            description: 'You have already taken the Commitment',
          });
          return;
        }

        await takePledge({
          duration: Math.min(Math.abs(pledgeDurationInt), 60),
        });

        showPopover({
          content: <CommitmentPass duration={pledgeDuration} />,
          overlayLook: true,
          style: {
            borderLeftWidth: 0,
            overflow: 'hidden',
            backgroundColor: dark.colors.background,
            borderRadius: 20,
            paddingBottom: 10,
          },
        });
      }
    }
  }, [takePledge, checkIfPledgeTakenResData, checkIfPledgeTaken, loading]);

  useEffect(() => {
    handleShowSharePledgeOverlay();
  }, []);
};

export default useHandleShowSharePledgeOverlay;

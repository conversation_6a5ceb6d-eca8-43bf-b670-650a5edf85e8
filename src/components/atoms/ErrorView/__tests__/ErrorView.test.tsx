import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ErrorView from '../ErrorView';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';

const mockedUseMediaQuery = useMediaQuery as jest.Mock;

beforeEach(() => {
  mockedUseMediaQuery.mockReturnValue({
    isMobile: false,
  });
});

describe('ErrorView', () => {
  it('renders without crashing', async () => {
    render(<ErrorView errorMessage="Test Error" />);
    await waitFor(() => {
      expect(true).toBe(true);
    });
  });

  it('displays the correct error message', async () => {
    const errorMessage = 'Something went wrong!';
    const { getByText } = render(<ErrorView errorMessage={errorMessage} />);
    await waitFor(() => {
      expect(getByText(errorMessage)).toBeTruthy();
    });
  });

  it('renders and calls onRetry when the Retry button is pressed', async () => {
    const handleRetry = jest.fn();
    const { getByText } = render(
      <ErrorView errorMessage="Error" onRetry={handleRetry} />,
    );
    const retryButton = getByText('Retry');

    fireEvent.press(retryButton);

    await waitFor(() => {
      expect(handleRetry).toHaveBeenCalledTimes(1);
    });
  });
});

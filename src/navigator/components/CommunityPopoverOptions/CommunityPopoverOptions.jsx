import { useRouter } from 'expo-router';
import React, { useCallback } from 'react';
import { Linking, Pressable, Text, View } from 'react-native';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { DISCORD_COMMUNITY_URL } from 'core/constants/appConstants';
import { closePopover } from 'molecules/Popover/Popover';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import styles from './CommunityPopoverOptions.style';
import Badge from '@/src/components/shared/Badge';
import useChatStore from '@/src/store/useChatStore';

const OptionRow = ({ iconConfig, onPress, label, shouldShowBadge = false }) => (
  <Pressable
    style={({ hovered }) => [
      styles.optionRowContainer,
      hovered && styles.hoveredOptionRowContainer,
    ]}
    onPress={onPress}
  >
    <View style={styles.optionRowContent}>
      {shouldShowBadge ? (
        <Badge containerStyle={styles.badgeContainer} />
      ) : null}
      <Icon color="white" size={12} {...iconConfig} />
      <Text style={styles.optionLabel}>{label}</Text>
    </View>
  </Pressable>
);

const CommunityPopoverOptions = () => {
  const router = useRouter();
  const { user } = useSession();
  const { isRead } = useChatStore((state) => ({
    isRead: state.isRead,
  }));
  const navigateToDiscord = useCallback(() => {
    Linking.openURL(DISCORD_COMMUNITY_URL);
    closePopover();
  }, []);

  const navigateToFriends = useCallback(() => {
    router.push(`/profile/${user?.username}/friends`);
    closePopover();
  }, [router, user?.username]);

  const navigateToMessages = useCallback(() => {
    router.push(`/chat`);
    closePopover();
  }, [router]);

  return (
    <View style={styles.modalContent}>
      <OptionRow
        onPress={navigateToFriends}
        iconConfig={{ name: 'person', type: ICON_TYPES.IONICON }}
        label="Friends"
      />
      <OptionRow
        onPress={navigateToMessages}
        iconConfig={{ name: 'message', type: ICON_TYPES.MATERIAL_ICONS }}
        label="Messages"
        shouldShowBadge={!isRead}
      />
      <OptionRow
        onPress={navigateToDiscord}
        iconConfig={{ name: 'discord', type: ICON_TYPES.FONT_AWESOME_6 }}
        label="Community"
      />
    </View>
  );
};

CommunityPopoverOptions.propTypes = {};

export default React.memo(CommunityPopoverOptions);

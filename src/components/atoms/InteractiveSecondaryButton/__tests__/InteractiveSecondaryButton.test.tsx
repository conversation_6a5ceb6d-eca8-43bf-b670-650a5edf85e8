import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import InteractiveSecondaryButton from '../index';

describe('InteractiveSecondaryButton', () => {
  it('renders without crashing', () => {
    render(
      <InteractiveSecondaryButton
        label="Test Button"
        onPress={() => {}}
        buttonStyle={{}}
        buttonContentStyle={{}}
      />,
    );
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <InteractiveSecondaryButton
        label="Test Button"
        onPress={onPress}
        buttonStyle={{}}
        buttonContentStyle={{}}
      />,
    );
    const button = getByText('Test Button');
    fireEvent.press(button);
    expect(onPress).toHaveBeenCalled();
  });
});

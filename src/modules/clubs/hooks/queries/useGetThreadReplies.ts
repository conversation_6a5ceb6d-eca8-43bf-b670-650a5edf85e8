import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import { CreatorInfo } from './useGetForumThreads';

const DEFAULT_PAGE_SIZE = 50;

const GET_THREAD_REPLIES = gql`
  query ForumReplies($threadId: ID!, $page: Int, $pageSize: Int) {
    forumReplies(threadId: $threadId, page: $page, pageSize: $pageSize) {
      results {
        id
        threadId
        content
        createdAt
        createdBy
        creatorInfo {
          username
          profileImageUrl
        }
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

export interface ThreadReply {
  id: string;
  threadId: string;
  content: string;
  createdAt: string;
  createdBy: string;
  creatorInfo: CreatorInfo;
}

const useGetThreadReplies = ({
  threadId,
  pageSize = DEFAULT_PAGE_SIZE,
}: {
  threadId: string;
  pageSize?: number;
}) => {
  const [fetchThreadRepliesQuery, { loading, error, refetch, client }] =
    useLazyQuery(GET_THREAD_REPLIES, {
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    });

  const fetchThreadReplies = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      if (loading) return;

      return fetchThreadRepliesQuery({
        variables: {
          threadId,
          page: pageNumber,
          pageSize,
        },
      });
    },
    [loading, fetchThreadRepliesQuery, threadId, pageSize],
  );

  const updateForumRepliesCache = useCallback(
    ({
      addedItems = [],
      removedItemIds = [],
      pageNumber = 1,
    }: {
      addedItems: ThreadReply[];
      removedItemIds: string[];
      pageNumber: number;
    }) => {
      client.cache.updateQuery(
        {
          query: GET_THREAD_REPLIES,
          variables: { page: pageNumber, pageSize, threadId },
          broadcast: true,
          overwrite: true,
        },
        (data) => {
          const { forumReplies } = data ?? EMPTY_OBJECT;

          const { results, totalResults } = forumReplies ?? EMPTY_OBJECT;

          if (_isEmpty(forumReplies)) {
            return data;
          }

          let updatedResults = results;

          if (!_isEmpty(addedItems)) {
            updatedResults = [...addedItems, ...updatedResults];
          }

          if (!_isEmpty(removedItemIds)) {
            updatedResults = _filter(
              updatedResults,
              (item) => !_includes(removedItemIds, item?._id),
            );
          }

          const updatedTotalItems =
            totalResults + _size(addedItems) - _size(removedItemIds);

          return {
            ...data,
            forumReplies: {
              ...forumReplies,
              results: updatedResults,
              totalResults: updatedTotalItems,
            },
          };
        },
      );
      const updatedCache = client.cache.readQuery({
        query: GET_THREAD_REPLIES,
        variables: { page: pageNumber, pageSize, threadId },
      });
      return updatedCache;
    },
    [client.cache, pageSize, threadId],
  );

  return {
    loading,
    error,
    fetchThreadReplies,
    refetch,
    updateForumRepliesCache,
  };
};
export { useGetThreadReplies };

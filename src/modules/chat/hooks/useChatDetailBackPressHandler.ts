import { useFocusEffect } from 'expo-router';
import { useCallback } from 'react';
import { BackHandler } from 'react-native';

const useChatDetailBackPressHandler = (onBackPress: () => boolean) => {
  useFocusEffect(
    useCallback(() => {
      BackHandler.addEventListener?.('hardwareBackPress', onBackPress);

      return () => {
        BackHandler.removeEventListener?.('hardwareBackPress', onBackPress);
      };
    }, [onBackPress]),
  );
};

export default useChatDetailBackPressHandler;

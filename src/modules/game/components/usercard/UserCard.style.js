import { StyleSheet } from 'react-native'
import Dark from '@/src/core/constants/themes/dark'

// Common styles for both web and mobile
const commonStyles = {
    background: {
        backgroundColor: Dark.colors.primary,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
    },
    emptyCard: {
        flex: 1,
        backgroundColor: Dark.colors.primary,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
    shadowCircle: {
        backgroundColor: Dark.colors.tertiary,
        alignSelf: 'center',
        borderRadius: 100,
    },
    emptyUser: {
        position: 'absolute',
        marginTop: -1,
    },
    userName: {
        fontSize: 12,
        color: Dark.colors.textDark,
        marginBottom: 4,
        maxWidth: 62,
        fontFamily: 'Montserrat-400',
    },
    userRating: {
        fontSize: 14,
        fontFamily: 'Montserrat-700',
        color: 'white',
    },
    image2: {
        height: 80,
        width: 66,
        position: 'absolute',
    },
    image1: {
        height: 98,
        width: 72,
    },
}

const mobileStyles = StyleSheet.create({
    ...commonStyles,
    background: {
        flex: 1,
        ...commonStyles.background,
    },
    card: {
        width: '100%',
        height: '100%',
        position: 'absolute',
        marginTop: -2,
        paddingTop: 6,
        justifyContent: 'space-around',
        alignItems: 'center',
        borderRadius: 12,
    },
    userInfo: {
        alignItems: 'center',
        marginTop: -4,
    },
})

const webStyles = StyleSheet.create({
    ...commonStyles,
    background: {
        width: 100,
        height: 108,
        ...commonStyles.background,
    },
    card: {
        width: 100,
        height: 108,
        marginTop: 0,
        gap: 8,
        position: 'absolute',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 12,
    },
    userInfo: {
        alignItems: 'center',
    },
})

export { mobileStyles, webStyles }

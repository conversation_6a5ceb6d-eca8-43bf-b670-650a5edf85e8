import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';

export const CARD_HEIGHT = 704;
export const MAX_CARD_WIDTH = 420;

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: 1,
        margin: 0,
        width: '100%',
        height: '100%',
        maxHeight: CARD_HEIGHT,
        maxWidth: MAX_CARD_WIDTH,
        backgroundColor: 'red',
    },
    captureViewContainer: {
        maxWidth: 418,
        width: '100%',
        flexDirection: 'column',
    },
    mobileBrowserContentContainer: {
        marginTop: 40,
    },
    gradientCardContainer: {
        maxWidth: 420,
        width: '100%',
        flexDirection: 'column',
    },
    cardContainer: {
        backgroundColor: dark.colors.background,
        maxHeight: CARD_HEIGHT,
        height: isCompactMode ? 460 : 460, 
        width: 360,
        maxWidth: MAX_CARD_WIDTH,
        borderWidth: 1,
        justifyContent:"center",
        alignItems: "center",
        borderColor: dark.colors.tertiary,
        padding: 18,
        gap:24,
        paddingBottom: 24,
        borderRadius: 18,
    },
    metricsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        gap: 8,
        marginTop: 10,
        marginBottom: 8
    },
    gradientBg: {
        flex: 1,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      
      },

})

const useGameResultOverlayStyles = () => {
    const { isMobile } = useMediaQuery();
    const styles = useMemo(() => createStyles(isMobile), [isMobile]);
    return styles;
  };


export default useGameResultOverlayStyles
